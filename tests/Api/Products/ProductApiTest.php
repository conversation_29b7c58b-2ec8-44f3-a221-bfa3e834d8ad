<?php

namespace Tests\Api\Products;

use Tests\Api\ApiTestCase;
use Tests\Builders\ProductBuilder;
use Tests\Builders\UserBuilder;
use App\Models\Product;
use App\Models\Category;

/**
 * ProductApiTest
 * Product API endpoint tests
 */
class ProductApiTest extends ApiTestCase
{
    /**
     * Test data
     */
    protected Product $product;
    protected Category $category;

    /**
     * Setup test data
     */
    protected function seedApiTestData(): void
    {
        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'slug' => 'test-category',
        ]);

        // Create test product
        $this->product = ProductBuilder::create()
            ->withName('Test Product')
            ->withCategory($this->category)
            ->active()
            ->create();
    }

    /**
     * Test get products list
     */
    public function test_it_can_get_products_list(): void
    {
        // Act
        $response = $this->getApi('/products');

        // Assert
        $this->assertApiSuccess($response);
        $this->assertApiPagination($response, [
            'id',
            'name',
            'slug',
            'price',
            'stock',
            'status',
            'featured',
        ]);
    }

    /**
     * Test get single product
     */
    public function test_it_can_get_single_product(): void
    {
        // Act
        $response = $this->getApi("/products/{$this->product->id}");

        // Assert
        $this->assertApiSuccess($response);
        $this->assertApiResource($response, [
            'id',
            'name',
            'slug',
            'description',
            'price',
            'stock',
            'sku',
            'status',
            'featured',
            'category',
            'images',
            'attributes',
            'created_at',
            'updated_at',
        ]);

        // Assert product data
        $productData = $response->json('data');
        $this->assertEquals($this->product->id, $productData['id']);
        $this->assertEquals($this->product->name, $productData['name']);
        $this->assertEquals($this->product->slug, $productData['slug']);
    }

    /**
     * Test get product by slug
     */
    public function test_it_can_get_product_by_slug(): void
    {
        // Act
        $response = $this->getApi("/products/slug/{$this->product->slug}");

        // Assert
        $this->assertApiSuccess($response);
        $productData = $response->json('data');
        $this->assertEquals($this->product->id, $productData['id']);
    }

    /**
     * Test create product (authenticated)
     */
    public function test_it_can_create_product(): void
    {
        // Arrange
        $productData = [
            'name' => 'New Test Product',
            'description' => 'New test product description',
            'price' => 150.00,
            'stock' => 20,
            'sku' => 'NEW-TEST-001',
            'category_id' => $this->category->id,
            'status' => 'active',
            'featured' => false,
        ];

        // Act
        $response = $this->adminRequest('POST', '/products', $productData);

        // Assert
        $this->assertApiSuccess($response, 201);
        $this->assertDatabaseHasRecord('products', [
            'name' => 'New Test Product',
            'sku' => 'NEW-TEST-001',
        ]);

        $createdProduct = $response->json('data');
        $this->assertEquals($productData['name'], $createdProduct['name']);
        $this->assertEquals($productData['price'], $createdProduct['price']);
    }

    /**
     * Test update product
     */
    public function test_it_can_update_product(): void
    {
        // Arrange
        $updateData = [
            'name' => 'Updated Product Name',
            'price' => 200.00,
            'stock' => 15,
        ];

        // Act
        $response = $this->adminRequest('PUT', "/products/{$this->product->id}", $updateData);

        // Assert
        $this->assertApiSuccess($response);
        $this->assertDatabaseHasRecord('products', [
            'id' => $this->product->id,
            'name' => 'Updated Product Name',
            'price' => 200.00,
        ]);

        $updatedProduct = $response->json('data');
        $this->assertEquals($updateData['name'], $updatedProduct['name']);
        $this->assertEquals($updateData['price'], $updatedProduct['price']);
    }

    /**
     * Test delete product
     */
    public function test_it_can_delete_product(): void
    {
        // Act
        $response = $this->adminRequest('DELETE', "/products/{$this->product->id}");

        // Assert
        $this->assertApiSuccess($response, 204);
        $this->assertSoftDeletedRecord('products', [
            'id' => $this->product->id,
        ]);
    }

    /**
     * Test product validation errors
     */
    public function test_it_validates_product_creation(): void
    {
        // Test missing required fields
        $response = $this->adminRequest('POST', '/products', []);
        $this->assertApiValidationError($response, ['name', 'price', 'sku']);

        // Test invalid price
        $response = $this->adminRequest('POST', '/products', [
            'name' => 'Test Product',
            'price' => -10,
            'sku' => 'TEST-001',
        ]);
        $this->assertApiValidationError($response, ['price']);

        // Test duplicate SKU
        $response = $this->adminRequest('POST', '/products', [
            'name' => 'Another Product',
            'price' => 100,
            'sku' => $this->product->sku, // Duplicate SKU
        ]);
        $this->assertApiValidationError($response, ['sku']);
    }

    /**
     * Test product filtering
     */
    public function test_it_can_filter_products(): void
    {
        // Create additional test products
        ProductBuilder::create()
            ->withName('Expensive Product')
            ->withPrice(500.00)
            ->withCategory($this->category)
            ->create();

        ProductBuilder::create()
            ->withName('Cheap Product')
            ->withPrice(10.00)
            ->withCategory($this->category)
            ->create();

        // Test price filtering
        $response = $this->getApi('/products?min_price=100&max_price=200');
        $this->assertApiSuccess($response);

        // Test category filtering
        $response = $this->getApi("/products?category_id={$this->category->id}");
        $this->assertApiSuccess($response);

        // Test status filtering
        $response = $this->getApi('/products?status=active');
        $this->assertApiSuccess($response);

        // Test featured filtering
        $response = $this->getApi('/products?featured=true');
        $this->assertApiSuccess($response);
    }

    /**
     * Test product search
     */
    public function test_it_can_search_products(): void
    {
        // Test search by name
        $response = $this->getApi('/products?search=Test');
        $this->assertApiSuccess($response);

        // Test search by description
        $response = $this->getApi('/products?search=description');
        $this->assertApiSuccess($response);

        // Test search by SKU
        $response = $this->getApi("/products?search={$this->product->sku}");
        $this->assertApiSuccess($response);
    }

    /**
     * Test product sorting
     */
    public function test_it_can_sort_products(): void
    {
        // Test sort by name
        $response = $this->getApi('/products?sort=name&order=asc');
        $this->assertApiSuccess($response);

        // Test sort by price
        $response = $this->getApi('/products?sort=price&order=desc');
        $this->assertApiSuccess($response);

        // Test sort by created_at
        $response = $this->getApi('/products?sort=created_at&order=desc');
        $this->assertApiSuccess($response);
    }

    /**
     * Test product pagination
     */
    public function test_it_can_paginate_products(): void
    {
        // Create multiple products
        ProductBuilder::create()->createMany(25);

        // Test pagination
        $response = $this->getApi('/products?page=1&per_page=10');
        $this->assertApiSuccess($response);
        $this->assertApiPagination($response);

        $paginationData = $response->json('meta.pagination');
        $this->assertEquals(1, $paginationData['current_page']);
        $this->assertEquals(10, $paginationData['per_page']);
        $this->assertGreaterThanOrEqual(25, $paginationData['total']);
    }

    /**
     * Test product authentication requirements
     */
    public function test_it_requires_authentication_for_write_operations(): void
    {
        // Test create without authentication
        $response = $this->postApi('/products', [
            'name' => 'Test Product',
            'price' => 100,
            'sku' => 'TEST-001',
        ]);
        $this->assertApiAuthenticationError($response);

        // Test update without authentication
        $response = $this->putApi("/products/{$this->product->id}", [
            'name' => 'Updated Name',
        ]);
        $this->assertApiAuthenticationError($response);

        // Test delete without authentication
        $response = $this->deleteApi("/products/{$this->product->id}");
        $this->assertApiAuthenticationError($response);
    }

    /**
     * Test product authorization
     */
    public function test_it_requires_proper_permissions(): void
    {
        // Create regular user (not admin)
        $user = UserBuilder::create()->customer()->create();

        // Test create with insufficient permissions
        $response = $this->authenticatedRequest('POST', '/products', [
            'name' => 'Test Product',
            'price' => 100,
            'sku' => 'TEST-001',
        ]);
        $this->assertApiAuthorizationError($response);
    }

    /**
     * Test product not found
     */
    public function test_it_returns_not_found_for_invalid_product(): void
    {
        $response = $this->getApi('/products/999999');
        $this->assertApiNotFound($response);
    }

    /**
     * Test API versioning
     */
    public function test_it_supports_api_versioning(): void
    {
        $response = $this->getApi("/products/{$this->product->id}");
        $this->assertApiVersion($response, 'v1');
    }

    /**
     * Test API rate limiting (if enabled)
     */
    public function test_it_respects_rate_limiting(): void
    {
        // Enable rate limiting for this test
        config(['api_rate_limiting.general.enabled' => true]);

        $response = $this->getApi('/products');
        $this->assertRateLimitHeaders($response);
    }

    /**
     * Test complete CRUD operations
     */
    public function test_complete_product_crud_operations(): void
    {
        $createData = [
            'name' => 'CRUD Test Product',
            'description' => 'Product for CRUD testing',
            'price' => 99.99,
            'stock' => 50,
            'sku' => 'CRUD-TEST-001',
            'category_id' => $this->category->id,
            'status' => 'active',
        ];

        $updateData = [
            'name' => 'Updated CRUD Product',
            'price' => 149.99,
            'stock' => 30,
        ];

        $this->testApiCrudOperations('products', $createData, $updateData);
    }

    /**
     * Test API error handling
     */
    public function test_api_error_handling(): void
    {
        $this->testApiErrorHandling('/products');
    }
}
