<?php

namespace Tests\Unit\Cache;

use Tests\TestCase;
use App\Core\Infrastructure\Cache\Services\SmartCacheInvalidator;
use App\Core\Infrastructure\Cache\Contracts\CacheTagManagerInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheAnalyticsInterface;
use Illuminate\Support\Facades\Cache;
use Mockery;

/**
 * Smart Cache Invalidator Test
 */
class SmartCacheInvalidatorTest extends TestCase
{
    protected SmartCacheInvalidator $invalidator;
    protected $mockTagManager;
    protected $mockAnalytics;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockTagManager = Mockery::mock(CacheTagManagerInterface::class);
        $this->mockAnalytics = Mockery::mock(CacheAnalyticsInterface::class);
        
        $this->invalidator = new SmartCacheInvalidator(
            $this->mockTagManager,
            $this->mockAnalytics,
            [
                'max_cascade_depth' => 3,
                'batch_size' => 50,
                'selective_threshold' => 0.8,
            ]
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_invalidate_single_key()
    {
        Cache::shouldReceive('forget')
            ->once()
            ->with('test_key')
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once()
            ->with('cache_invalidation', Mockery::type('array'));

        $result = $this->invalidator->invalidate('test_key');

        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_invalidate_multiple_keys()
    {
        $keys = ['key1', 'key2', 'key3'];

        Cache::shouldReceive('forget')
            ->times(3)
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once()
            ->with('cache_invalidation', Mockery::type('array'));

        $result = $this->invalidator->invalidate($keys);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_invalidate_by_tags()
    {
        $tags = ['product', 'category'];
        $keys = ['product_1', 'product_2', 'category_1'];

        $this->mockTagManager->shouldReceive('getEntityTags')
            ->andReturn($keys);

        Cache::shouldReceive('tags')
            ->with($tags[0])
            ->andReturnSelf();
        Cache::shouldReceive('getKeys')
            ->andReturn(['product_1', 'product_2']);

        Cache::shouldReceive('tags')
            ->with($tags[1])
            ->andReturnSelf();
        Cache::shouldReceive('getKeys')
            ->andReturn(['category_1']);

        Cache::shouldReceive('forget')
            ->times(3)
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once();

        $result = $this->invalidator->invalidateByTags($tags);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_set_invalidation_strategy()
    {
        $this->invalidator->setStrategy('delayed');

        // Strategy'nin ayarlandığını doğrula
        $this->assertInstanceOf(SmartCacheInvalidator::class, $this->invalidator);
    }

    /** @test */
    public function it_throws_exception_for_invalid_strategy()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid invalidation strategy: invalid');

        $this->invalidator->setStrategy('invalid');
    }

    /** @test */
    public function it_can_perform_batch_invalidation()
    {
        $items = [
            ['key' => 'key1'],
            ['key' => 'key2'],
            ['key' => 'key3'],
        ];

        Cache::shouldReceive('forget')
            ->times(3)
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->times(1); // Batch olarak tek kayıt

        $results = $this->invalidator->batchInvalidate($items);

        $this->assertIsArray($results);
        $this->assertCount(1, $results); // Tek batch
        $this->assertEquals('success', $results[0]['status']);
        $this->assertEquals(3, $results[0]['keys_count']);
    }

    /** @test */
    public function it_can_perform_cascade_invalidation()
    {
        $entityType = 'product';
        $entityId = 123;

        // Entity tags
        $this->mockTagManager->shouldReceive('getEntityTags')
            ->with($entityType, $entityId)
            ->andReturn(['product_123', 'category_456']);

        Cache::shouldReceive('forget')
            ->times(2)
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once();

        $result = $this->invalidator->cascadeInvalidate($entityType, $entityId);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_handles_invalidation_errors_gracefully()
    {
        Cache::shouldReceive('forget')
            ->once()
            ->with('error_key')
            ->andThrow(new \Exception('Cache error'));

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once();

        $result = $this->invalidator->invalidate('error_key');

        $this->assertFalse($result);
    }

    /** @test */
    public function it_records_invalidation_history()
    {
        Cache::shouldReceive('forget')
            ->once()
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once();

        $this->invalidator->invalidate('test_key');

        $history = $this->invalidator->getInvalidationHistory();

        $this->assertIsArray($history);
        $this->assertCount(1, $history);
        $this->assertEquals(['test_key'], $history[0]['keys']);
        $this->assertEquals('success', $history[0]['status']);
    }

    /** @test */
    public function it_limits_invalidation_history()
    {
        // History limit'i düşük ayarla
        $invalidator = new SmartCacheInvalidator(
            $this->mockTagManager,
            $this->mockAnalytics,
            ['history_limit' => 2]
        );

        Cache::shouldReceive('forget')
            ->times(3)
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->times(3);

        // 3 invalidation işlemi yap
        $invalidator->invalidate('key1');
        $invalidator->invalidate('key2');
        $invalidator->invalidate('key3');

        $history = $invalidator->getInvalidationHistory();

        // Sadece son 2 kayıt olmalı
        $this->assertCount(2, $history);
        $this->assertEquals(['key2'], $history[0]['keys']);
        $this->assertEquals(['key3'], $history[1]['keys']);
    }

    /** @test */
    public function it_can_invalidate_by_pattern()
    {
        // Redis pattern matching mock
        Cache::shouldReceive('getDefaultDriver')
            ->andReturn('redis');

        // Redis facade mock
        \Illuminate\Support\Facades\Redis::shouldReceive('keys')
            ->with('product_*')
            ->andReturn(['product_1', 'product_2', 'product_3']);

        Cache::shouldReceive('forget')
            ->times(3)
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once();

        $result = $this->invalidator->invalidateByPattern('product_*');

        $this->assertTrue($result);
    }

    /** @test */
    public function it_handles_empty_key_lists_gracefully()
    {
        // Boş tag listesi
        $this->mockTagManager->shouldReceive('getEntityTags')
            ->andReturn([]);

        Cache::shouldReceive('tags')
            ->andReturnSelf();
        Cache::shouldReceive('getKeys')
            ->andReturn([]);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once();

        $result = $this->invalidator->invalidateByTags(['empty_tag']);

        $this->assertTrue($result); // Boş liste için true döner
    }

    /** @test */
    public function it_uses_selective_invalidation_strategy()
    {
        // Selective strategy ayarla
        $this->invalidator->setStrategy('selective');

        // Hit rate mock'u
        $this->mockAnalytics->shouldReceive('getStatistics')
            ->andReturn([
                'keys' => [
                    'low_hit_key' => ['hit_rate' => 0.5], // Threshold'un altında
                    'high_hit_key' => ['hit_rate' => 0.9], // Threshold'un üstünde
                ]
            ]);

        // Sadece düşük hit rate'li key silinmeli
        Cache::shouldReceive('forget')
            ->once()
            ->with('low_hit_key')
            ->andReturn(true);

        $this->mockAnalytics->shouldReceive('recordEvent')
            ->once();

        $result = $this->invalidator->invalidate(['low_hit_key', 'high_hit_key'], [
            'strategy' => 'selective',
            'threshold' => 0.8
        ]);

        $this->assertTrue($result);
    }
}
