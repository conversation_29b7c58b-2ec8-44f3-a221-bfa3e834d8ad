<?php

namespace Tests\Unit\Cache;

use App\Core\Infrastructure\Cache\Services\CacheAnalyticsService;
use App\Core\Infrastructure\Cache\Services\IntelligentCacheWarmer;
use App\Core\Infrastructure\Cache\Services\MultiLevelCacheManager;
use App\Core\Infrastructure\Cache\Services\SmartCacheInvalidator;
use App\Core\Infrastructure\Cache\Contracts\CacheAnalyticsInterface;
use App\Core\Infrastructure\Cache\Contracts\MultiLevelCacheInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheInvalidatorInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 * Advanced Caching System Test
 * Phase 5B: Advanced Caching System test sınıfı
 */
class AdvancedCachingSystemTest extends TestCase
{
    use RefreshDatabase;

    protected CacheAnalyticsInterface $analytics;
    protected MultiLevelCacheInterface $multiLevelCache;
    protected CacheInvalidatorInterface $invalidator;
    protected IntelligentCacheWarmer $warmer;

    protected function setUp(): void
    {
        parent::setUp();

        // Test için cache events tablosunu oluştur
        $this->createCacheEventsTable();

        // Service'leri initialize et
        $this->analytics = app(CacheAnalyticsInterface::class);
        $this->multiLevelCache = app(MultiLevelCacheInterface::class);
        $this->invalidator = app(CacheInvalidatorInterface::class);
        $this->warmer = app(IntelligentCacheWarmer::class);
    }

    /** @test */
    public function it_can_record_cache_events()
    {
        // Arrange
        $eventData = [
            'key' => 'test_key',
            'response_time' => 150,
            'entity_type' => 'product',
        ];

        // Act
        $result = $this->analytics->recordEvent('cache_hit', $eventData);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseHas('cache_events', [
            'event' => 'cache_hit',
            'cache_key' => 'test_key',
            'entity_type' => 'product',
        ]);
    }

    /** @test */
    public function it_can_get_cache_statistics()
    {
        // Arrange
        $this->seedCacheEvents();

        // Act
        $stats = $this->analytics->getStatistics();

        // Assert
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('hit_rate', $stats);
        $this->assertArrayHasKey('total_requests', $stats);
        $this->assertArrayHasKey('cache_hits', $stats);
        $this->assertArrayHasKey('cache_misses', $stats);
    }

    /** @test */
    public function it_can_get_hit_miss_ratio()
    {
        // Arrange
        $this->seedCacheEvents();

        // Act
        $ratio = $this->analytics->getHitMissRatio('product', 'day');

        // Assert
        $this->assertIsArray($ratio);
        $this->assertArrayHasKey('hit_rate', $ratio);
        $this->assertArrayHasKey('miss_rate', $ratio);
        $this->assertArrayHasKey('total_hits', $ratio);
        $this->assertArrayHasKey('total_misses', $ratio);
    }

    /** @test */
    public function it_can_get_top_keys()
    {
        // Arrange
        $this->seedCacheEvents();

        // Act
        $topKeys = $this->analytics->getTopKeys(5, 'day');

        // Assert
        $this->assertIsArray($topKeys);
        $this->assertLessThanOrEqual(5, count($topKeys));
        
        if (!empty($topKeys)) {
            $this->assertArrayHasKey('key', $topKeys[0]);
            $this->assertArrayHasKey('total_requests', $topKeys[0]);
            $this->assertArrayHasKey('hit_rate', $topKeys[0]);
        }
    }

    /** @test */
    public function it_can_check_health_status()
    {
        // Act
        $health = $this->analytics->getHealthStatus();

        // Assert
        $this->assertIsArray($health);
        $this->assertArrayHasKey('overall_status', $health);
        $this->assertArrayHasKey('checks', $health);
        $this->assertContains($health['overall_status'], ['healthy', 'warning', 'critical', 'error']);
    }

    /** @test */
    public function multi_level_cache_can_store_and_retrieve_data()
    {
        // Arrange
        $key = 'test_multi_level_key';
        $value = ['data' => 'test_value', 'timestamp' => time()];

        // Act
        $putResult = $this->multiLevelCache->put($key, $value, 300);
        $retrievedValue = $this->multiLevelCache->get($key);

        // Assert
        $this->assertTrue($putResult);
        $this->assertEquals($value, $retrievedValue);
    }

    /** @test */
    public function multi_level_cache_can_forget_data()
    {
        // Arrange
        $key = 'test_forget_key';
        $value = 'test_value';
        $this->multiLevelCache->put($key, $value, 300);

        // Act
        $forgetResult = $this->multiLevelCache->forget($key);
        $retrievedValue = $this->multiLevelCache->get($key);

        // Assert
        $this->assertTrue($forgetResult);
        $this->assertNull($retrievedValue);
    }

    /** @test */
    public function multi_level_cache_can_get_level_statistics()
    {
        // Act
        $stats = $this->multiLevelCache->getLevelStatistics();

        // Assert
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('level_1', $stats);
        $this->assertArrayHasKey('level_2', $stats);
        $this->assertArrayHasKey('level_3', $stats);
    }

    /** @test */
    public function cache_invalidator_can_invalidate_keys()
    {
        // Arrange
        $keys = ['key1', 'key2', 'key3'];
        foreach ($keys as $key) {
            Cache::put($key, 'value', 300);
        }

        // Act
        $result = $this->invalidator->invalidate($keys);

        // Assert
        $this->assertTrue($result);
        foreach ($keys as $key) {
            $this->assertNull(Cache::get($key));
        }
    }

    /** @test */
    public function cache_invalidator_can_invalidate_by_pattern()
    {
        // Arrange
        Cache::put('product_1', 'value1', 300);
        Cache::put('product_2', 'value2', 300);
        Cache::put('category_1', 'value3', 300);

        // Act
        $result = $this->invalidator->invalidateByPattern('product_*');

        // Assert
        $this->assertTrue($result);
        $this->assertNull(Cache::get('product_1'));
        $this->assertNull(Cache::get('product_2'));
        $this->assertNotNull(Cache::get('category_1')); // Bu etkilenmemeli
    }

    /** @test */
    public function intelligent_cache_warmer_can_estimate_warming_time()
    {
        // Act
        $estimate = $this->warmer->estimateWarmingTime('product', ['batch_size' => 50]);

        // Assert
        $this->assertIsArray($estimate);
        $this->assertArrayHasKey('estimated_duration', $estimate);
        $this->assertArrayHasKey('estimated_items', $estimate);
        $this->assertArrayHasKey('confidence', $estimate);
    }

    /** @test */
    public function it_can_generate_cache_reports()
    {
        // Arrange
        $this->seedCacheEvents();

        // Act
        $summaryReport = $this->analytics->generateReport('summary', ['period' => 'day']);
        $detailedReport = $this->analytics->generateReport('detailed', ['period' => 'day']);

        // Assert
        $this->assertIsArray($summaryReport);
        $this->assertArrayHasKey('type', $summaryReport);
        $this->assertEquals('summary', $summaryReport['type']);

        $this->assertIsArray($detailedReport);
        $this->assertArrayHasKey('type', $detailedReport);
        $this->assertEquals('detailed', $detailedReport['type']);
    }

    /**
     * Test için cache events tablosunu oluştur
     */
    protected function createCacheEventsTable(): void
    {
        if (!DB::getSchemaBuilder()->hasTable('cache_events')) {
            DB::statement('
                CREATE TABLE cache_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event VARCHAR(50) NOT NULL,
                    cache_key VARCHAR(500),
                    entity_type VARCHAR(100),
                    entity_id VARCHAR(100),
                    data TEXT,
                    response_time INTEGER,
                    memory_usage BIGINT,
                    cache_level INTEGER,
                    cache_driver VARCHAR(50),
                    session_id VARCHAR(100),
                    user_id VARCHAR(100),
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ');
        }
    }

    /**
     * Test için cache events verisi oluştur
     */
    protected function seedCacheEvents(): void
    {
        $events = [
            ['event' => 'cache_hit', 'cache_key' => 'product_1', 'entity_type' => 'product', 'response_time' => 50],
            ['event' => 'cache_hit', 'cache_key' => 'product_2', 'entity_type' => 'product', 'response_time' => 75],
            ['event' => 'cache_miss', 'cache_key' => 'product_3', 'entity_type' => 'product', 'response_time' => 200],
            ['event' => 'cache_hit', 'cache_key' => 'category_1', 'entity_type' => 'category', 'response_time' => 30],
            ['event' => 'cache_miss', 'cache_key' => 'category_2', 'entity_type' => 'category', 'response_time' => 150],
        ];

        foreach ($events as $event) {
            DB::table('cache_events')->insert(array_merge($event, [
                'data' => json_encode(['test' => true]),
                'memory_usage' => rand(1000000, 5000000),
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
