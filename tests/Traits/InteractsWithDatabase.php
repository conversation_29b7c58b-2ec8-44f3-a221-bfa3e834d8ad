<?php

namespace Tests\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

/**
 * InteractsWithDatabase Trait
 * Database test helper'ları
 */
trait InteractsWithDatabase
{
    /**
     * Test database'ini setup et
     */
    protected function setupTestDatabase(): void
    {
        // Migration'ları çalıştır
        $this->artisan('migrate:fresh');

        // Seeder'ları çalıştır (test için)
        $this->artisan('db:seed', ['--class' => 'TestSeeder']);
    }

    /**
     * Database'de record'un varlığını assert et
     */
    protected function assertDatabaseHasRecord(string $table, array $data): void
    {
        $this->assertDatabaseHas($table, $data);
    }

    /**
     * Database'de record'un yokluğunu assert et
     */
    protected function assertDatabaseMissingRecord(string $table, array $data): void
    {
        $this->assertDatabaseMissing($table, $data);
    }

    /**
     * Soft delete'li record'u assert et
     */
    protected function assertSoftDeletedRecord(string $table, array $data): void
    {
        $this->assertDatabaseHas($table, array_merge($data, [
            'deleted_at' => function ($value) {
                return !is_null($value);
            }
        ]));
    }

    /**
     * Database transaction'ını test et
     */
    protected function assertDatabaseTransaction(callable $callback): void
    {
        DB::beginTransaction();

        try {
            $callback();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Database constraint'lerini test et
     */
    protected function assertDatabaseConstraints(string $table, array $constraints): void
    {
        foreach ($constraints as $constraint => $expected) {
            switch ($constraint) {
                case 'foreign_keys':
                    $this->assertForeignKeyConstraints($table, $expected);
                    break;
                case 'unique_keys':
                    $this->assertUniqueConstraints($table, $expected);
                    break;
                case 'not_null':
                    $this->assertNotNullConstraints($table, $expected);
                    break;
            }
        }
    }

    /**
     * Foreign key constraint'lerini assert et
     */
    protected function assertForeignKeyConstraints(string $table, array $foreignKeys): void
    {
        foreach ($foreignKeys as $column => $referencedTable) {
            // Foreign key constraint'in var olduğunu kontrol et
            $this->assertTrue(
                $this->hasForeignKeyConstraint($table, $column, $referencedTable),
                "Foreign key constraint not found: {$table}.{$column} -> {$referencedTable}"
            );
        }
    }

    /**
     * Unique constraint'leri assert et
     */
    protected function assertUniqueConstraints(string $table, array $uniqueColumns): void
    {
        foreach ($uniqueColumns as $column) {
            $this->assertTrue(
                $this->hasUniqueConstraint($table, $column),
                "Unique constraint not found: {$table}.{$column}"
            );
        }
    }

    /**
     * Not null constraint'leri assert et
     */
    protected function assertNotNullConstraints(string $table, array $notNullColumns): void
    {
        foreach ($notNullColumns as $column) {
            $this->assertTrue(
                $this->hasNotNullConstraint($table, $column),
                "Not null constraint not found: {$table}.{$column}"
            );
        }
    }

    /**
     * Foreign key constraint'in varlığını kontrol et
     */
    protected function hasForeignKeyConstraint(string $table, string $column, string $referencedTable): bool
    {
        $foreignKeys = Schema::getConnection()->getDoctrineSchemaManager()
            ->listTableForeignKeys($table);

        foreach ($foreignKeys as $foreignKey) {
            if (in_array($column, $foreignKey->getLocalColumns()) &&
                $foreignKey->getForeignTableName() === $referencedTable) {
                return true;
            }
        }

        return false;
    }

    /**
     * Unique constraint'in varlığını kontrol et
     */
    protected function hasUniqueConstraint(string $table, string $column): bool
    {
        $indexes = Schema::getConnection()->getDoctrineSchemaManager()
            ->listTableIndexes($table);

        foreach ($indexes as $index) {
            if ($index->isUnique() && in_array($column, $index->getColumns())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Not null constraint'in varlığını kontrol et
     */
    protected function hasNotNullConstraint(string $table, string $column): bool
    {
        $columns = Schema::getConnection()->getDoctrineSchemaManager()
            ->listTableColumns($table);

        return isset($columns[$column]) && $columns[$column]->getNotnull();
    }

    /**
     * Database performance'ını test et
     */
    protected function assertDatabasePerformance(callable $callback, int $maxQueries = 10, float $maxTime = 1.0): void
    {
        $startTime = microtime(true);
        $startQueries = $this->getQueryCount();

        $callback();

        $endTime = microtime(true);
        $endQueries = $this->getQueryCount();

        $executionTime = $endTime - $startTime;
        $queryCount = $endQueries - $startQueries;

        $this->assertLessThanOrEqual(
            $maxQueries,
            $queryCount,
            "Too many database queries executed: {$queryCount} (max: {$maxQueries})"
        );

        $this->assertLessThanOrEqual(
            $maxTime,
            $executionTime,
            "Database operation took too long: {$executionTime}s (max: {$maxTime}s)"
        );
    }

    /**
     * Query count'unu al
     */
    protected function getQueryCount(): int
    {
        return count(DB::getQueryLog());
    }

    /**
     * Database query log'unu başlat
     */
    protected function enableQueryLog(): void
    {
        DB::enableQueryLog();
    }

    /**
     * Database query log'unu al
     */
    protected function getQueryLog(): array
    {
        return DB::getQueryLog();
    }

    /**
     * Test data'sını temizle
     */
    protected function cleanupTestData(): void
    {
        // Test sırasında oluşturulan temporary data'yı temizle
        DB::table('temporary_test_data')->delete();

        // Cache'i temizle
        \Cache::flush();

        // File uploads'ları temizle
        $this->cleanupTestUploads();
    }

    /**
     * Test upload'larını temizle
     */
    protected function cleanupTestUploads(): void
    {
        $testUploadPath = storage_path('testing/uploads');
        if (is_dir($testUploadPath)) {
            $files = glob($testUploadPath . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Model factory'sini kullanarak test data oluştur
     */
    protected function createTestData(string $modelClass, int $count = 1, array $attributes = []): Model|\Illuminate\Database\Eloquent\Collection
    {
        if ($count === 1) {
            return $modelClass::factory()->create($attributes);
        }

        return $modelClass::factory()->count($count)->create($attributes);
    }

    /**
     * Database seeding'ini test et
     */
    protected function assertSeederWorks(string $seederClass): void
    {
        $this->artisan('db:seed', ['--class' => $seederClass])
             ->assertExitCode(0);
    }

    /**
     * Migration'ı test et
     */
    protected function assertMigrationWorks(string $migrationFile): void
    {
        $this->artisan('migrate', ['--path' => $migrationFile])
             ->assertExitCode(0);
    }
}
