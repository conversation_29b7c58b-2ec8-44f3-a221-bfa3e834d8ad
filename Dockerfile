FROM php:8.2-fpm

# Sistem bağımlılıklarını yükleme
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    supervisor

# PHP uzantılarını yükleme
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# Redis uzantısını yükleme
RUN pecl install redis && docker-php-ext-enable redis

# Composer yükleme
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Supervisor için dizinleri oluşturma
RUN mkdir -p /var/log/supervisor

# Çalışma dizinini ayarlama
WORKDIR /var/www

# Uygulama dosyalarını kopyalama
COPY . /var/www

# Composer bağımlılıklarını yükleme
RUN composer install --no-interaction

# Gerek<PERSON> izinleri ayarlama
RUN chmod -R 777 /var/www/storage /var/www/bootstrap/cache

# Supervisor yapılandırmasını kopyalama
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Entrypoint ve yardımcı scriptleri kopyalama
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh
COPY docker/queue-manager.sh /usr/local/bin/queue-manager

# Script'leri çalıştırılabilir yapma
RUN chmod +x /usr/local/bin/entrypoint.sh /usr/local/bin/queue-manager

# Environment değişkenleri
ENV QUEUE_STRATEGY=horizon
ENV AUTO_MIGRATE=false
ENV CLEAR_FAILED_JOBS=false

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/queue-manager health || exit 1

# Entrypoint ayarlama
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
