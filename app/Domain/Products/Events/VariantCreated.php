<?php

namespace App\Domain\Products\Events;

use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class VariantCreated implements DomainEvent
{
    private ProductVariant $variant;
    private Carbon $occurredOn;

    public function __construct(ProductVariant $variant)
    {
        $this->variant = $variant;
        $this->occurredOn = Carbon::now();
    }

    public function getVariant(): ProductVariant
    {
        return $this->variant;
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'product.variant_created';
    }

    public function getEventData(): array
    {
        return [
            'variant_id' => $this->variant->getId(),
            'product_id' => $this->variant->getProductId(),
            'sku' => $this->variant->getSKU()->getValue(),
            'attribute_values' => $this->variant->getAttributeValues(),
            'additional_price' => $this->variant->getAdditionalPrice()->getAmount(),
            'stock_quantity' => $this->variant->getStock()->getQuantity(),
            'status' => $this->variant->getStatus(),
            'is_default' => $this->variant->isDefault(),
            'created_at' => $this->variant->getCreatedAt()->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->variant->getId();
    }

    public function getAggregateType(): string
    {
        return 'ProductVariant';
    }
}
