<?php

namespace App\Domain\Products\Events;

use App\Domain\Products\Entities\Product;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class ProductCreated implements DomainEvent
{
    private Product $product;
    private Carbon $occurredOn;

    public function __construct(Product $product)
    {
        $this->product = $product;
        $this->occurredOn = Carbon::now();
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'product.created';
    }

    public function getEventData(): array
    {
        return [
            'product_id' => $this->product->getId(),
            'name' => $this->product->getName(),
            'slug' => $this->product->getSlug(),
            'sku' => $this->product->getSKU()->getValue(),
            'price' => $this->product->getPrice()->getAmount(),
            'currency' => $this->product->getPrice()->getCurrency(),
            'stock_quantity' => $this->product->getStock()->getQuantity(),
            'category_id' => $this->product->getCategoryId(),
            'status' => $this->product->getStatus(),
            'is_featured' => $this->product->isFeatured(),
            'created_at' => $this->product->getCreatedAt()->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->product->getId();
    }

    public function getAggregateType(): string
    {
        return 'Product';
    }
}
