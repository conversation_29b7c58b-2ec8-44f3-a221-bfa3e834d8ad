<?php

namespace App\Domain\Shipping\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;
use App\Core\Domain\ValueObjects\Money;

/**
 * ShippingRate Value Object
 * Kargo ücreti için immutable value object
 */
class ShippingRate extends ValueObject
{
    private Money $baseCost;
    private Money $insuranceCost;
    private Money $handlingCost;
    private Money $fuelSurcharge;
    private Money $taxes;
    private Money $discountAmount;
    private Money $totalCost;
    private string $currency;
    private array $breakdown;
    private array $metadata;

    private function __construct(
        Money $baseCost,
        Money $insuranceCost = null,
        Money $handlingCost = null,
        Money $fuelSurcharge = null,
        Money $taxes = null,
        Money $discountAmount = null,
        array $breakdown = [],
        array $metadata = []
    ) {
        $this->baseCost = $baseCost;
        $this->insuranceCost = $insuranceCost ?: Money::zero($baseCost->getCurrency());
        $this->handlingCost = $handlingCost ?: Money::zero($baseCost->getCurrency());
        $this->fuelSurcharge = $fuelSurcharge ?: Money::zero($baseCost->getCurrency());
        $this->taxes = $taxes ?: Money::zero($baseCost->getCurrency());
        $this->discountAmount = $discountAmount ?: Money::zero($baseCost->getCurrency());
        $this->currency = $baseCost->getCurrency();
        $this->breakdown = $breakdown;
        $this->metadata = $metadata;

        // Toplam maliyeti hesapla
        $this->totalCost = $this->calculateTotalCost();
    }

    /**
     * Basit kargo ücreti oluştur
     */
    public static function simple(Money $baseCost): self
    {
        return new self($baseCost);
    }

    /**
     * Detaylı kargo ücreti oluştur
     */
    public static function detailed(
        Money $baseCost,
        Money $insuranceCost = null,
        Money $handlingCost = null,
        Money $fuelSurcharge = null,
        Money $taxes = null,
        Money $discountAmount = null,
        array $breakdown = [],
        array $metadata = []
    ): self {
        return new self(
            $baseCost,
            $insuranceCost,
            $handlingCost,
            $fuelSurcharge,
            $taxes,
            $discountAmount,
            $breakdown,
            $metadata
        );
    }

    /**
     * Array'den ShippingRate oluştur
     */
    public static function fromArray(array $data, string $currency = 'TRY'): self
    {
        return new self(
            baseCost: Money::fromAmount($data['base_cost'] ?? 0, $currency),
            insuranceCost: isset($data['insurance_cost']) ? Money::fromAmount($data['insurance_cost'], $currency) : null,
            handlingCost: isset($data['handling_cost']) ? Money::fromAmount($data['handling_cost'], $currency) : null,
            fuelSurcharge: isset($data['fuel_surcharge']) ? Money::fromAmount($data['fuel_surcharge'], $currency) : null,
            taxes: isset($data['taxes']) ? Money::fromAmount($data['taxes'], $currency) : null,
            discountAmount: isset($data['discount_amount']) ? Money::fromAmount($data['discount_amount'], $currency) : null,
            breakdown: $data['breakdown'] ?? [],
            metadata: $data['metadata'] ?? []
        );
    }

    /**
     * Ücretsiz kargo
     */
    public static function free(string $currency = 'TRY'): self
    {
        return new self(
            Money::zero($currency),
            null,
            null,
            null,
            null,
            null,
            ['type' => 'free_shipping'],
            ['free_shipping' => true]
        );
    }

    /**
     * Temel maliyeti getir
     */
    public function getBaseCost(): Money
    {
        return $this->baseCost;
    }

    /**
     * Sigorta maliyetini getir
     */
    public function getInsuranceCost(): Money
    {
        return $this->insuranceCost;
    }

    /**
     * İşlem maliyetini getir
     */
    public function getHandlingCost(): Money
    {
        return $this->handlingCost;
    }

    /**
     * Yakıt ek ücretini getir
     */
    public function getFuelSurcharge(): Money
    {
        return $this->fuelSurcharge;
    }

    /**
     * Vergileri getir
     */
    public function getTaxes(): Money
    {
        return $this->taxes;
    }

    /**
     * İndirim tutarını getir
     */
    public function getDiscountAmount(): Money
    {
        return $this->discountAmount;
    }

    /**
     * Toplam maliyeti getir
     */
    public function getTotalCost(): Money
    {
        return $this->totalCost;
    }

    /**
     * Para birimini getir
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * Maliyet dökümünü getir
     */
    public function getBreakdown(): array
    {
        return $this->breakdown;
    }

    /**
     * Metadata'yı getir
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Belirli metadata değerini getir
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Toplam maliyeti hesapla
     */
    private function calculateTotalCost(): Money
    {
        return $this->baseCost
            ->add($this->insuranceCost)
            ->add($this->handlingCost)
            ->add($this->fuelSurcharge)
            ->add($this->taxes)
            ->subtract($this->discountAmount);
    }

    /**
     * Ücretsiz kargo mu kontrol et
     */
    public function isFree(): bool
    {
        return $this->totalCost->isZero() || $this->getMetadataValue('free_shipping', false);
    }

    /**
     * Sigorta dahil mi kontrol et
     */
    public function hasInsurance(): bool
    {
        return !$this->insuranceCost->isZero();
    }

    /**
     * İşlem ücreti dahil mi kontrol et
     */
    public function hasHandlingFee(): bool
    {
        return !$this->handlingCost->isZero();
    }

    /**
     * Yakıt ek ücreti dahil mi kontrol et
     */
    public function hasFuelSurcharge(): bool
    {
        return !$this->fuelSurcharge->isZero();
    }

    /**
     * Vergi dahil mi kontrol et
     */
    public function hasTaxes(): bool
    {
        return !$this->taxes->isZero();
    }

    /**
     * İndirim uygulanmış mı kontrol et
     */
    public function hasDiscount(): bool
    {
        return !$this->discountAmount->isZero();
    }

    /**
     * İndirim yüzdesini hesapla
     */
    public function getDiscountPercentage(): float
    {
        if ($this->baseCost->isZero()) {
            return 0.0;
        }

        return ($this->discountAmount->getAmount() / $this->baseCost->getAmount()) * 100;
    }

    /**
     * Vergi yüzdesini hesapla
     */
    public function getTaxPercentage(): float
    {
        $subtotal = $this->baseCost->add($this->insuranceCost)->add($this->handlingCost)->add($this->fuelSurcharge);
        
        if ($subtotal->isZero()) {
            return 0.0;
        }

        return ($this->taxes->getAmount() / $subtotal->getAmount()) * 100;
    }

    /**
     * Ara toplam getir (vergiler hariç)
     */
    public function getSubtotal(): Money
    {
        return $this->baseCost
            ->add($this->insuranceCost)
            ->add($this->handlingCost)
            ->add($this->fuelSurcharge)
            ->subtract($this->discountAmount);
    }

    /**
     * Detaylı maliyet dökümünü getir
     */
    public function getDetailedBreakdown(): array
    {
        $breakdown = [
            'base_cost' => [
                'label' => 'Temel Kargo Ücreti',
                'amount' => $this->baseCost->getAmount(),
                'currency' => $this->currency
            ]
        ];

        if ($this->hasInsurance()) {
            $breakdown['insurance_cost'] = [
                'label' => 'Sigorta Ücreti',
                'amount' => $this->insuranceCost->getAmount(),
                'currency' => $this->currency
            ];
        }

        if ($this->hasHandlingFee()) {
            $breakdown['handling_cost'] = [
                'label' => 'İşlem Ücreti',
                'amount' => $this->handlingCost->getAmount(),
                'currency' => $this->currency
            ];
        }

        if ($this->hasFuelSurcharge()) {
            $breakdown['fuel_surcharge'] = [
                'label' => 'Yakıt Ek Ücreti',
                'amount' => $this->fuelSurcharge->getAmount(),
                'currency' => $this->currency
            ];
        }

        if ($this->hasDiscount()) {
            $breakdown['discount'] = [
                'label' => 'İndirim',
                'amount' => -$this->discountAmount->getAmount(),
                'currency' => $this->currency
            ];
        }

        $breakdown['subtotal'] = [
            'label' => 'Ara Toplam',
            'amount' => $this->getSubtotal()->getAmount(),
            'currency' => $this->currency
        ];

        if ($this->hasTaxes()) {
            $breakdown['taxes'] = [
                'label' => 'Vergiler',
                'amount' => $this->taxes->getAmount(),
                'currency' => $this->currency
            ];
        }

        $breakdown['total'] = [
            'label' => 'Toplam',
            'amount' => $this->totalCost->getAmount(),
            'currency' => $this->currency
        ];

        return $breakdown;
    }

    /**
     * Kargo ücreti kategorisini getir
     */
    public function getCostCategory(): string
    {
        $amount = $this->totalCost->getAmount();

        if ($amount == 0) {
            return 'free';
        } elseif ($amount <= 10) {
            return 'low';
        } elseif ($amount <= 30) {
            return 'medium';
        } elseif ($amount <= 60) {
            return 'high';
        } else {
            return 'premium';
        }
    }

    /**
     * Kargo ücreti kategorisi adını getir
     */
    public function getCostCategoryName(): string
    {
        return match ($this->getCostCategory()) {
            'free' => 'Ücretsiz',
            'low' => 'Düşük',
            'medium' => 'Orta',
            'high' => 'Yüksek',
            'premium' => 'Premium',
            default => 'Bilinmeyen'
        };
    }

    /**
     * Formatlanmış toplam tutarı getir
     */
    public function getFormattedTotal(): string
    {
        if ($this->isFree()) {
            return 'Ücretsiz';
        }

        return $this->totalCost->format();
    }

    /**
     * Kısa özet getir
     */
    public function getSummary(): string
    {
        if ($this->isFree()) {
            return 'Ücretsiz Kargo';
        }

        $parts = [$this->getFormattedTotal()];

        if ($this->hasDiscount()) {
            $parts[] = sprintf('(%%.1f%% indirim)', $this->getDiscountPercentage());
        }

        if ($this->hasInsurance()) {
            $parts[] = 'Sigortalı';
        }

        return implode(' - ', $parts);
    }

    /**
     * İndirim uygula
     */
    public function applyDiscount(Money $discountAmount): self
    {
        return new self(
            $this->baseCost,
            $this->insuranceCost,
            $this->handlingCost,
            $this->fuelSurcharge,
            $this->taxes,
            $discountAmount,
            $this->breakdown,
            $this->metadata
        );
    }

    /**
     * Vergi ekle
     */
    public function addTax(Money $taxAmount): self
    {
        return new self(
            $this->baseCost,
            $this->insuranceCost,
            $this->handlingCost,
            $this->fuelSurcharge,
            $taxAmount,
            $this->discountAmount,
            $this->breakdown,
            $this->metadata
        );
    }

    /**
     * Sigorta ekle
     */
    public function addInsurance(Money $insuranceAmount): self
    {
        return new self(
            $this->baseCost,
            $insuranceAmount,
            $this->handlingCost,
            $this->fuelSurcharge,
            $this->taxes,
            $this->discountAmount,
            $this->breakdown,
            $this->metadata
        );
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && 
               $this->totalCost->equals($other->totalCost) &&
               $this->currency === $other->currency;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'base_cost' => $this->baseCost->getAmount(),
            'insurance_cost' => $this->insuranceCost->getAmount(),
            'handling_cost' => $this->handlingCost->getAmount(),
            'fuel_surcharge' => $this->fuelSurcharge->getAmount(),
            'taxes' => $this->taxes->getAmount(),
            'discount_amount' => $this->discountAmount->getAmount(),
            'subtotal' => $this->getSubtotal()->getAmount(),
            'total_cost' => $this->totalCost->getAmount(),
            'currency' => $this->currency,
            'is_free' => $this->isFree(),
            'has_insurance' => $this->hasInsurance(),
            'has_handling_fee' => $this->hasHandlingFee(),
            'has_fuel_surcharge' => $this->hasFuelSurcharge(),
            'has_taxes' => $this->hasTaxes(),
            'has_discount' => $this->hasDiscount(),
            'discount_percentage' => $this->getDiscountPercentage(),
            'tax_percentage' => $this->getTaxPercentage(),
            'cost_category' => $this->getCostCategory(),
            'cost_category_name' => $this->getCostCategoryName(),
            'formatted_total' => $this->getFormattedTotal(),
            'summary' => $this->getSummary(),
            'detailed_breakdown' => $this->getDetailedBreakdown(),
            'breakdown' => $this->breakdown,
            'metadata' => $this->metadata,
        ];
    }
}
