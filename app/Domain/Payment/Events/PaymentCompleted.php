<?php

namespace App\Domain\Payment\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Payment\Entities\Payment;
use Carbon\Carbon;

/**
 * PaymentCompleted Domain Event
 * Ödeme tamamlandığında tetiklenir
 */
class PaymentCompleted implements DomainEvent
{
    private Payment $payment;
    private Carbon $occurredOn;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Ödemeyi getir
     */
    public function getPayment(): Payment
    {
        return $this->payment;
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'payment.completed';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'payment_id' => $this->payment->getId(),
            'transaction_id' => $this->payment->getTransactionId()->getValue(),
            'order_id' => $this->payment->getOrderId(),
            'user_id' => $this->payment->getUserId(),
            'amount' => $this->payment->getAmount()->getAmount()->getAmount(),
            'net_amount' => $this->payment->getAmount()->getNetAmount()->getAmount(),
            'fee_amount' => $this->payment->getAmount()->getFeeAmount()->getAmount(),
            'currency' => $this->payment->getAmount()->getCurrency(),
            'gateway' => $this->payment->getGateway()->getProvider(),
            'payment_method' => $this->payment->getPaymentMethod(),
            'status' => $this->payment->getStatus(),
            'completed_at' => $this->payment->getCompletedAt()?->toISOString(),
            'event_time' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->payment->getId();
    }
}
