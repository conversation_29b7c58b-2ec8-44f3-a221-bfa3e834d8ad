<?php

namespace App\Modules\Products\Application\UseCases;

use App\Core\Application\UseCase;
use App\Core\Domain\Exceptions\EntityNotFoundException;
use App\Modules\Products\Application\DTOs\ManageStockDTO;
use App\Modules\Products\Domain\Interfaces\ProductRepositoryInterface;
use App\Modules\Products\Domain\Models\Product;
use App\Modules\Products\Domain\Events\StockUpdated;

/**
 * Manage Stock Use Case
 * Stok yönetimi use case'i
 */
class ManageStockUseCase extends UseCase
{
    /**
     * Product repository
     *
     * @var ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;

    /**
     * Constructor
     *
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * Use case'i çalıştır
     *
     * @param ManageStockDTO $dto
     * @return Product
     * @throws EntityNotFoundException
     * @throws \InvalidArgumentException
     */
    public function execute($dto = null): Product
    {
        if (!$dto instanceof ManageStockDTO) {
            throw new \InvalidArgumentException('Invalid input type. Expected ManageStockDTO.');
        }

        // Ürünü getir
        $product = $this->productRepository->findByIdOrFail($dto->productId);
        
        // Mevcut stok miktarını kaydet
        $previousStock = $product->stock;

        // Operasyona göre stok güncelle
        $newStock = $this->calculateNewStock($product, $dto);

        // Stok güncellemesi yap
        $product->updateStock($newStock);

        // Domain event fırlat
        $product->addDomainEvent(new StockUpdated(
            $product,
            $previousStock,
            $newStock,
            $dto->operation,
            $dto->reason,
            $dto->userId,
            $dto->reference
        ));

        return $product;
    }

    /**
     * Input'u validate et
     *
     * @param mixed $input
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validateInput($input): void
    {
        if (!$input instanceof ManageStockDTO) {
            throw new \InvalidArgumentException('Input must be an instance of ManageStockDTO');
        }
    }

    /**
     * Yeni stok miktarını hesapla
     *
     * @param Product $product
     * @param ManageStockDTO $dto
     * @return int
     * @throws \InvalidArgumentException
     */
    private function calculateNewStock(Product $product, ManageStockDTO $dto): int
    {
        $currentStock = $product->stock;

        switch ($dto->operation) {
            case 'increase':
                return $currentStock + $dto->quantity;

            case 'decrease':
                $newStock = $currentStock - $dto->quantity;
                if ($newStock < 0) {
                    throw new \InvalidArgumentException(
                        "Insufficient stock. Current: {$currentStock}, Requested: {$dto->quantity}"
                    );
                }
                return $newStock;

            case 'set':
                return $dto->quantity;

            default:
                throw new \InvalidArgumentException("Invalid operation: {$dto->operation}");
        }
    }

    /**
     * Toplu stok güncelleme
     *
     * @param array $stockUpdates ManageStockDTO array'i
     * @return array Updated products
     */
    public function bulkUpdate(array $stockUpdates): array
    {
        $updatedProducts = [];

        foreach ($stockUpdates as $stockUpdate) {
            if (!$stockUpdate instanceof ManageStockDTO) {
                throw new \InvalidArgumentException('All items must be ManageStockDTO instances');
            }

            $updatedProducts[] = $this->execute($stockUpdate);
        }

        return $updatedProducts;
    }

    /**
     * Stok rezervasyonu (geçici stok azaltma)
     *
     * @param int $productId
     * @param int $quantity
     * @param string $reservationId
     * @param int|null $userId
     * @return Product
     */
    public function reserveStock(int $productId, int $quantity, string $reservationId, ?int $userId = null): Product
    {
        $dto = ManageStockDTO::forDecrease(
            $productId,
            $quantity,
            'Stock reservation',
            $userId,
            $reservationId
        );

        return $this->execute($dto);
    }

    /**
     * Stok rezervasyonunu iptal et (stoku geri ekle)
     *
     * @param int $productId
     * @param int $quantity
     * @param string $reservationId
     * @param int|null $userId
     * @return Product
     */
    public function cancelReservation(int $productId, int $quantity, string $reservationId, ?int $userId = null): Product
    {
        $dto = ManageStockDTO::forIncrease(
            $productId,
            $quantity,
            'Reservation cancelled',
            $userId,
            $reservationId
        );

        return $this->execute($dto);
    }

    /**
     * Stok transferi (bir üründen diğerine)
     *
     * @param int $fromProductId
     * @param int $toProductId
     * @param int $quantity
     * @param string $reason
     * @param int|null $userId
     * @return array [fromProduct, toProduct]
     */
    public function transferStock(
        int $fromProductId,
        int $toProductId,
        int $quantity,
        string $reason = 'Stock transfer',
        ?int $userId = null
    ): array {
        $transferId = 'transfer_' . uniqid();

        // Kaynak üründen stok azalt
        $fromProduct = $this->execute(ManageStockDTO::forDecrease(
            $fromProductId,
            $quantity,
            $reason,
            $userId,
            $transferId
        ));

        // Hedef ürüne stok ekle
        $toProduct = $this->execute(ManageStockDTO::forIncrease(
            $toProductId,
            $quantity,
            $reason,
            $userId,
            $transferId
        ));

        return [$fromProduct, $toProduct];
    }
}
