<?php

namespace App\Modules\Products\Application\UseCases;

use App\Core\Application\UseCase;
use App\Core\Domain\Exceptions\EntityNotFoundException;
use App\Modules\Products\Application\DTOs\UpdateProductDTO;
use App\Modules\Products\Domain\Interfaces\ProductRepositoryInterface;
use App\Modules\Products\Domain\Models\Product;
use App\Modules\Products\Domain\ValueObjects\ProductSku;
use App\Modules\Products\Domain\ValueObjects\ProductSlug;
use Illuminate\Support\Str;

/**
 * Update Product Use Case
 * Ürün güncelleme use case'i
 */
class UpdateProductUseCase extends UseCase
{
    /**
     * Product repository
     *
     * @var ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;

    /**
     * Constructor
     *
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * Use case'i çalıştır
     *
     * @param UpdateProductDTO $dto
     * @return Product
     * @throws EntityNotFoundException
     * @throws \InvalidArgumentException
     */
    public function execute($dto = null): Product
    {
        if (!$dto instanceof UpdateProductDTO) {
            throw new \InvalidArgumentException('Invalid input type. Expected UpdateProductDTO.');
        }

        // Değişiklik var mı kontrol et
        if (!$dto->hasChanges()) {
            // Değişiklik yoksa mevcut ürünü döndür
            return $this->productRepository->findByIdOrFail($dto->id);
        }

        // Mevcut ürünü getir
        $existingProduct = $this->productRepository->findByIdOrFail($dto->id);

        // Güncelleme verilerini hazırla
        $updateData = $dto->toModelArray();

        // İsim değişmişse slug'ı güncelle
        if (isset($updateData['name'])) {
            $updateData['slug'] = $this->generateUniqueSlug($updateData['name'], $dto->id);
        }

        // SKU değişmişse kontrol et
        if (isset($updateData['sku'])) {
            $this->validateSku($updateData['sku'], $dto->id);
        }

        // İndirim logic'i kontrol et
        if (isset($updateData['is_on_sale']) || isset($updateData['sale_price'])) {
            $this->validateSaleLogic($updateData, $existingProduct);
        }

        // Ürünü güncelle
        $updatedProduct = $this->productRepository->update($dto->id, $updateData);

        return $updatedProduct;
    }

    /**
     * Input'u validate et
     *
     * @param mixed $input
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validateInput($input): void
    {
        if (!$input instanceof UpdateProductDTO) {
            throw new \InvalidArgumentException('Input must be an instance of UpdateProductDTO');
        }
    }

    /**
     * Benzersiz slug oluştur
     *
     * @param string $name
     * @param int $excludeId
     * @return string
     */
    private function generateUniqueSlug(string $name, int $excludeId): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        // Slug benzersiz olana kadar dene (mevcut ürün hariç)
        while (!$this->productRepository->isSlugUnique($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        // ProductSlug value object ile validate et
        $slugValueObject = new ProductSlug($slug);
        
        return $slugValueObject->getValue();
    }

    /**
     * SKU'yu validate et
     *
     * @param string $sku
     * @param int $excludeId
     * @throws \InvalidArgumentException
     */
    private function validateSku(string $sku, int $excludeId): void
    {
        // SKU format kontrolü
        $skuValueObject = new ProductSku($sku);

        // Benzersizlik kontrolü (mevcut ürün hariç)
        if (!$this->productRepository->isSkuUnique($sku, $excludeId)) {
            throw new \InvalidArgumentException("SKU '{$sku}' already exists");
        }
    }

    /**
     * İndirim logic'ini validate et
     *
     * @param array $updateData
     * @param Product $existingProduct
     * @throws \InvalidArgumentException
     */
    private function validateSaleLogic(array $updateData, Product $existingProduct): void
    {
        $isOnSale = $updateData['is_on_sale'] ?? $existingProduct->is_on_sale;
        $salePrice = $updateData['sale_price'] ?? $existingProduct->sale_price;
        $regularPrice = $updateData['price'] ?? $existingProduct->price;

        // İndirimde ise sale_price zorunlu
        if ($isOnSale && (!$salePrice || $salePrice <= 0)) {
            throw new \InvalidArgumentException('Sale price is required when product is on sale');
        }

        // İndirim fiyatı normal fiyattan düşük olmalı
        if ($isOnSale && $salePrice >= $regularPrice) {
            throw new \InvalidArgumentException('Sale price must be less than regular price');
        }

        // İndirimde değilse sale_price'ı temizle
        if (!$isOnSale) {
            $updateData['sale_price'] = null;
            $updateData['sale_starts_at'] = null;
            $updateData['sale_ends_at'] = null;
        }
    }
}
