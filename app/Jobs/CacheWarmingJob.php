<?php

namespace App\Jobs;

use App\Core\Infrastructure\Cache\Contracts\CacheWarmerInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheAnalyticsInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * Cache Warming Job
 * Background cache warming işlemi için job sınıfı
 */
class CacheWarmingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $warmerType;
    protected array $options;
    protected int $maxRetries = 3;
    protected int $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(string $warmerType, array $options = [])
    {
        $this->warmerType = $warmerType;
        $this->options = $options;
        
        // Job konfigürasyonu
        $this->onQueue('cache-warming');
        $this->tries = $this->maxRetries;
        $this->timeout = $this->timeout;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);

        try {
            Log::info('Cache warming job started', [
                'warmer_type' => $this->warmerType,
                'options' => $this->options,
                'job_id' => $this->job->getJobId(),
            ]);

            // Warmer'ı resolve et
            $warmer = $this->resolveWarmer($this->warmerType);
            
            if (!$warmer) {
                throw new \Exception("Cache warmer not found for type: {$this->warmerType}");
            }

            if (!$warmer->isEnabled()) {
                Log::info('Cache warmer is disabled, skipping', [
                    'warmer_type' => $this->warmerType,
                ]);
                return;
            }

            // Cache warming işlemini çalıştır
            $result = $warmer->warm($this->options);

            $duration = microtime(true) - $startTime;

            // Sonucu logla
            Log::info('Cache warming job completed', [
                'warmer_type' => $this->warmerType,
                'result' => $result,
                'duration' => $duration,
                'job_id' => $this->job->getJobId(),
            ]);

            // Analytics'e kaydet
            $this->recordAnalytics($result, $duration);

        } catch (\Exception $e) {
            $duration = microtime(true) - $startTime;

            Log::error('Cache warming job failed', [
                'warmer_type' => $this->warmerType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'duration' => $duration,
                'job_id' => $this->job->getJobId(),
                'attempt' => $this->attempts(),
            ]);

            // Analytics'e hata kaydı
            $this->recordAnalytics([
                'status' => 'error',
                'message' => $e->getMessage(),
                'items_warmed' => 0,
            ], $duration);

            // Retry logic
            if ($this->attempts() < $this->maxRetries) {
                $delay = $this->calculateRetryDelay();
                $this->release($delay);
                
                Log::info('Cache warming job will be retried', [
                    'warmer_type' => $this->warmerType,
                    'attempt' => $this->attempts(),
                    'delay' => $delay,
                ]);
            } else {
                Log::error('Cache warming job failed permanently', [
                    'warmer_type' => $this->warmerType,
                    'attempts' => $this->attempts(),
                ]);
                
                $this->fail($e);
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Cache warming job failed permanently', [
            'warmer_type' => $this->warmerType,
            'error' => $exception->getMessage(),
            'job_id' => $this->job->getJobId(),
        ]);

        // Analytics'e kalıcı hata kaydı
        $this->recordAnalytics([
            'status' => 'failed',
            'message' => $exception->getMessage(),
            'items_warmed' => 0,
        ], 0);
    }

    /**
     * Warmer'ı resolve et
     */
    protected function resolveWarmer(string $type): ?CacheWarmerInterface
    {
        try {
            // Service container'dan warmer'ı al
            $warmerClass = $this->getWarmerClass($type);
            
            if (!$warmerClass || !class_exists($warmerClass)) {
                return null;
            }

            return app($warmerClass);

        } catch (\Exception $e) {
            Log::error('Failed to resolve cache warmer', [
                'type' => $type,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Warmer sınıfını al
     */
    protected function getWarmerClass(string $type): ?string
    {
        $warmerClasses = [
            'product' => \App\Core\Infrastructure\Cache\Warmers\ProductCacheWarmer::class,
            'category' => \App\Core\Infrastructure\Cache\Warmers\CategoryCacheWarmer::class,
            'user' => \App\Core\Infrastructure\Cache\Warmers\UserCacheWarmer::class,
            'order' => \App\Core\Infrastructure\Cache\Warmers\OrderCacheWarmer::class,
            'cart' => \App\Core\Infrastructure\Cache\Warmers\CartCacheWarmer::class,
            'inventory' => \App\Core\Infrastructure\Cache\Warmers\InventoryCacheWarmer::class,
            'shipping' => \App\Core\Infrastructure\Cache\Warmers\ShippingCacheWarmer::class,
            'notification' => \App\Core\Infrastructure\Cache\Warmers\NotificationCacheWarmer::class,
        ];

        return $warmerClasses[$type] ?? null;
    }

    /**
     * Retry gecikmesini hesapla
     */
    protected function calculateRetryDelay(): int
    {
        // Exponential backoff: 2^attempt * 30 seconds
        return min(pow(2, $this->attempts()) * 30, 300); // Max 5 dakika
    }

    /**
     * Analytics'e kaydet
     */
    protected function recordAnalytics(array $result, float $duration): void
    {
        try {
            $analytics = app(CacheAnalyticsInterface::class);
            
            $analytics->recordEvent('cache_warming_job', [
                'warmer_type' => $this->warmerType,
                'status' => $result['status'] ?? 'unknown',
                'items_warmed' => $result['items_warmed'] ?? 0,
                'duration' => $duration,
                'memory_used' => $result['memory_used'] ?? 0,
                'errors_count' => count($result['errors'] ?? []),
                'job_id' => $this->job->getJobId(),
                'attempt' => $this->attempts(),
                'options' => $this->options,
            ]);

        } catch (\Exception $e) {
            Log::warning('Failed to record cache warming analytics', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Job'un unique ID'sini al
     */
    public function uniqueId(): string
    {
        return "cache_warming_{$this->warmerType}_" . md5(serialize($this->options));
    }

    /**
     * Job'un tag'lerini al
     */
    public function tags(): array
    {
        return [
            'cache-warming',
            "warmer:{$this->warmerType}",
            'background-job',
        ];
    }
}
