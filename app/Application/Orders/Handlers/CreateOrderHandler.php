<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Commands\CreateOrderCommand;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Entities\Order;
use App\Domain\Orders\Entities\OrderItem;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateOrderHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(CreateOrderCommand $command): OrderDTO
    {
        return DB::transaction(function () use ($command) {
            try {
                // Sipariş numarası oluştur
                $orderNumber = OrderNumber::generate();
                
                // Toplam tutarı hesapla
                $totalAmount = $this->calculateTotalAmount($command->getItems());
                
                // Adres bilgilerini oluştur
                $billingAddress = $this->createAddress($command->getBillingAddress(), 'billing');
                $shippingAddress = $this->createAddress($command->getShippingAddress(), 'shipping');
                
                // Sipariş oluştur
                $order = Order::create(
                    userId: $command->getUserId(),
                    orderNumber: $orderNumber,
                    totalAmount: new Money($totalAmount),
                    paymentMethod: $command->getPaymentMethod(),
                    shippingMethod: $command->getShippingMethod(),
                    billingAddress: $billingAddress,
                    shippingAddress: $shippingAddress
                );

                // Sipariş öğelerini ekle
                foreach ($command->getItems() as $itemData) {
                    $orderItem = OrderItem::create(
                        productId: $itemData['product_id'],
                        productName: $itemData['product_name'],
                        price: new Money($itemData['price']),
                        quantity: $itemData['quantity'],
                        options: $itemData['options'] ?? []
                    );
                    $order->addItem($orderItem);
                }

                // Ek ücretleri ayarla
                if ($command->getShippingCost() !== null) {
                    $order->setShippingCost(new Money($command->getShippingCost()));
                }

                if ($command->getTaxAmount() !== null) {
                    $order->setTaxAmount(new Money($command->getTaxAmount()));
                }

                if ($command->getDiscountAmount() !== null) {
                    $order->setDiscountAmount(new Money($command->getDiscountAmount()));
                }

                if ($command->getCouponCode() !== null) {
                    $order->setCouponCode($command->getCouponCode());
                }

                // Not ekle
                if ($command->getNotes() !== null) {
                    $order->addNote($command->getNotes(), 'general', false);
                }

                // Siparişi kaydet
                $savedOrder = $this->orderRepository->save($order);

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedOrder);

                Log::info('Order created successfully', [
                    'order_id' => $savedOrder->getId(),
                    'order_number' => $savedOrder->getOrderNumber()->getValue(),
                    'user_id' => $savedOrder->getUserId(),
                    'total_amount' => $savedOrder->getTotalAmount()->getAmount()
                ]);

                return OrderDTO::fromEntity($savedOrder);

            } catch (\Exception $e) {
                Log::error('Failed to create order', [
                    'user_id' => $command->getUserId(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }

    private function calculateTotalAmount(array $items): float
    {
        $total = 0;
        foreach ($items as $item) {
            $total += $item['price'] * $item['quantity'];
        }
        return $total;
    }

    private function createAddress(?array $addressData, string $type): ?Address
    {
        if (!$addressData) {
            return null;
        }

        return new Address(
            name: $addressData['name'],
            phone: $addressData['phone'],
            address: $addressData['address'],
            city: $addressData['city'],
            state: $addressData['state'],
            country: $addressData['country'],
            type: $type,
            email: $addressData['email'] ?? null,
            zipcode: $addressData['zipcode'] ?? null,
            district: $addressData['district'] ?? null
        );
    }
}
