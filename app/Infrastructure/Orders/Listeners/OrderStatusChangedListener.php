<?php

namespace App\Infrastructure\Orders\Listeners;

use App\Domain\Orders\Events\OrderStatusChanged;
use App\Infrastructure\Orders\Services\OrderNotificationService;
use App\Enums\OrderStatus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;

/**
 * Order Status Changed Event Listener
 * Sipariş durumu değiştiğinde çalışan infrastructure listener
 */
class OrderStatusChangedListener
{
    private OrderNotificationService $notificationService;

    public function __construct(OrderNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Event'i handle et
     */
    public function handle(OrderStatusChanged $event): void
    {
        $order = $event->getOrder();
        $oldStatus = $event->getOldStatus();
        $newStatus = $event->getNewStatus();

        try {
            // 1. Cache'i temizle
            $this->clearStatusCaches($order, $oldStatus, $newStatus);

            // 2. Bildirim gönder
            $this->sendNotifications($order, $oldStatus, $newStatus);

            // 3. Status'a özel işlemler
            $this->handleStatusSpecificActions($order, $oldStatus, $newStatus);

            // 4. Analytics'e kaydet
            $this->recordStatusAnalytics($order, $oldStatus, $newStatus);

            // 5. External service'lere bildir
            $this->notifyExternalServices($order, $oldStatus, $newStatus);

            // 6. Workflow automation
            $this->triggerWorkflowAutomation($order, $newStatus);

            Log::info('Order status change successfully processed', [
                'order_id' => $order->getId(),
                'order_number' => $order->getOrderNumber()->getValue(),
                'old_status' => $oldStatus->value,
                'new_status' => $newStatus->value,
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing order status change event', [
                'order_id' => $order->getId(),
                'old_status' => $oldStatus->value,
                'new_status' => $newStatus->value,
                'error' => $e->getMessage(),
            ]);

            // Hata durumunda retry mekanizması
            $this->scheduleRetry($event);
        }
    }

    /**
     * Status cache'lerini temizle
     */
    private function clearStatusCaches($order, $oldStatus, $newStatus): void
    {
        $cacheKeys = [
            'orders.user.' . $order->getUserId() . '.*',
            'orders.status.' . $oldStatus->value . '.*',
            'orders.status.' . $newStatus->value . '.*',
            'orders.count.*',
            'orders.statistics',
        ];

        foreach ($cacheKeys as $pattern) {
            Cache::flush(); // Production'da daha spesifik olmalı
        }
    }

    /**
     * Bildirim gönder
     */
    private function sendNotifications($order, $oldStatus, $newStatus): void
    {
        // Müşteri ve admin bildirimlerini gönder
        $this->notificationService->sendOrderStatusChangedNotification($order, $oldStatus, $newStatus);

        // Status'a özel bildirimler
        switch ($newStatus) {
            case OrderStatus::PAYMENT_CONFIRMED:
                $this->notificationService->sendPaymentConfirmedNotification($order);
                break;
                
            case OrderStatus::SHIPPED:
                $this->notificationService->sendShippedNotification($order);
                break;
                
            case OrderStatus::DELIVERED:
                $this->notificationService->sendDeliveredNotification($order);
                break;
                
            case OrderStatus::CANCELLED:
                $this->notificationService->sendCancelledNotification($order);
                break;
        }
    }

    /**
     * Status'a özel işlemler
     */
    private function handleStatusSpecificActions($order, $oldStatus, $newStatus): void
    {
        switch ($newStatus) {
            case OrderStatus::PAYMENT_CONFIRMED:
                $this->handlePaymentConfirmed($order);
                break;
                
            case OrderStatus::PROCESSING:
                $this->handleProcessing($order);
                break;
                
            case OrderStatus::PREPARING:
                $this->handlePreparing($order);
                break;
                
            case OrderStatus::READY_TO_SHIP:
                $this->handleReadyToShip($order);
                break;
                
            case OrderStatus::SHIPPED:
                $this->handleShipped($order);
                break;
                
            case OrderStatus::DELIVERED:
                $this->handleDelivered($order);
                break;
                
            case OrderStatus::CANCELLED:
                $this->handleCancelled($order);
                break;
                
            case OrderStatus::RETURNED:
                $this->handleReturned($order);
                break;
                
            case OrderStatus::REFUNDED:
                $this->handleRefunded($order);
                break;
        }
    }

    /**
     * Ödeme onaylandı işlemleri
     */
    private function handlePaymentConfirmed($order): void
    {
        // Fatura oluştur
        Queue::push('generate-invoice', [
            'order_id' => $order->getId(),
        ]);

        // Stok rezervasyonunu onayla
        Queue::push('confirm-stock-reservation', [
            'order_id' => $order->getId(),
        ]);

        // Loyalty points hesapla
        Queue::push('calculate-loyalty-points', [
            'order_id' => $order->getId(),
            'user_id' => $order->getUserId(),
            'amount' => $order->getTotalAmount()->getAmount(),
        ]);
    }

    /**
     * İşleme alındı işlemleri
     */
    private function handleProcessing($order): void
    {
        // Depo bildirimini gönder
        Queue::push('notify-warehouse', [
            'order_id' => $order->getId(),
            'action' => 'start_processing',
            'priority' => $this->calculateProcessingPriority($order),
        ]);

        // Picking list oluştur
        Queue::push('generate-picking-list', [
            'order_id' => $order->getId(),
        ]);
    }

    /**
     * Hazırlanıyor işlemleri
     */
    private function handlePreparing($order): void
    {
        // Packing slip oluştur
        Queue::push('generate-packing-slip', [
            'order_id' => $order->getId(),
        ]);

        // Shipping label hazırla
        Queue::push('prepare-shipping-label', [
            'order_id' => $order->getId(),
            'shipping_method' => $order->getShippingMethod(),
        ]);
    }

    /**
     * Kargoya hazır işlemleri
     */
    private function handleReadyToShip($order): void
    {
        // Kargo şirketine bildir
        Queue::push('notify-shipping-company', [
            'order_id' => $order->getId(),
            'shipping_method' => $order->getShippingMethod(),
        ]);

        // Pickup schedule et
        Queue::push('schedule-pickup', [
            'order_id' => $order->getId(),
        ]);
    }

    /**
     * Kargoya verildi işlemleri
     */
    private function handleShipped($order): void
    {
        // Tracking bilgilerini güncelle
        Queue::push('update-tracking-info', [
            'order_id' => $order->getId(),
            'tracking_number' => $order->getTrackingNumber(),
        ]);

        // Delivery estimation hesapla
        Queue::push('calculate-delivery-estimation', [
            'order_id' => $order->getId(),
            'shipping_method' => $order->getShippingMethod(),
        ]);

        // Customer service'e bildir
        Queue::push('notify-customer-service', [
            'order_id' => $order->getId(),
            'action' => 'order_shipped',
        ]);
    }

    /**
     * Teslim edildi işlemleri
     */
    private function handleDelivered($order): void
    {
        // Stok rezervasyonunu kaldır
        Queue::push('release-stock-reservation', [
            'order_id' => $order->getId(),
        ]);

        // Customer satisfaction survey gönder (1 gün sonra)
        Queue::later(now()->addDay(), 'send-satisfaction-survey', [
            'order_id' => $order->getId(),
        ]);

        // Loyalty points'leri aktif et
        Queue::push('activate-loyalty-points', [
            'order_id' => $order->getId(),
            'user_id' => $order->getUserId(),
        ]);

        // Cross-sell recommendations gönder (3 gün sonra)
        Queue::later(now()->addDays(3), 'send-cross-sell-recommendations', [
            'order_id' => $order->getId(),
            'user_id' => $order->getUserId(),
        ]);
    }

    /**
     * İptal edildi işlemleri
     */
    private function handleCancelled($order): void
    {
        // Stok rezervasyonunu iptal et
        Queue::push('cancel-stock-reservation', [
            'order_id' => $order->getId(),
        ]);

        // Ödeme iadesi başlat (eğer ödeme yapılmışsa)
        if ($order->getPaymentStatus()->isPaid()) {
            Queue::push('initiate-refund', [
                'order_id' => $order->getId(),
                'reason' => 'order_cancelled',
            ]);
        }

        // Loyalty points'leri iptal et
        Queue::push('cancel-loyalty-points', [
            'order_id' => $order->getId(),
            'user_id' => $order->getUserId(),
        ]);
    }

    /**
     * İade edildi işlemleri
     */
    private function handleReturned($order): void
    {
        // Return processing başlat
        Queue::push('process-return', [
            'order_id' => $order->getId(),
        ]);

        // Quality control için bildir
        Queue::push('notify-quality-control', [
            'order_id' => $order->getId(),
            'action' => 'return_inspection',
        ]);
    }

    /**
     * İade edildi işlemleri
     */
    private function handleRefunded($order): void
    {
        // Refund işlemini tamamla
        Queue::push('complete-refund', [
            'order_id' => $order->getId(),
        ]);

        // Accounting'e bildir
        Queue::push('notify-accounting', [
            'order_id' => $order->getId(),
            'action' => 'refund_completed',
            'amount' => $order->getTotalAmount()->getAmount(),
        ]);
    }

    /**
     * Status analytics'i kaydet
     */
    private function recordStatusAnalytics($order, $oldStatus, $newStatus): void
    {
        $analyticsData = [
            'event' => 'order_status_changed',
            'order_id' => $order->getId(),
            'old_status' => $oldStatus->value,
            'new_status' => $newStatus->value,
            'user_id' => $order->getUserId(),
            'order_value' => $order->getTotalAmount()->getAmount(),
            'processing_time' => $this->calculateProcessingTime($order, $oldStatus, $newStatus),
            'timestamp' => now()->toISOString(),
        ];

        Queue::push('record-analytics', $analyticsData);
    }

    /**
     * External service'lere bildir
     */
    private function notifyExternalServices($order, $oldStatus, $newStatus): void
    {
        // CRM sistemine bildir
        if (config('integrations.crm.enabled', false)) {
            Queue::push('sync-crm-order-status', [
                'order_id' => $order->getId(),
                'old_status' => $oldStatus->value,
                'new_status' => $newStatus->value,
            ]);
        }

        // ERP sistemine bildir
        if (config('integrations.erp.enabled', false)) {
            Queue::push('sync-erp-order-status', [
                'order_id' => $order->getId(),
                'new_status' => $newStatus->value,
            ]);
        }
    }

    /**
     * Workflow automation tetikle
     */
    private function triggerWorkflowAutomation($order, $newStatus): void
    {
        Queue::push('trigger-workflow', [
            'trigger' => 'order_status_changed',
            'order_id' => $order->getId(),
            'status' => $newStatus->value,
            'user_id' => $order->getUserId(),
        ]);
    }

    /**
     * İşleme önceliğini hesapla
     */
    private function calculateProcessingPriority($order): string
    {
        $amount = $order->getTotalAmount()->getAmount();
        $itemCount = count($order->getItems());
        
        if ($amount > 2000 || $itemCount > 5) {
            return 'high';
        } elseif ($amount > 1000 || $itemCount > 3) {
            return 'medium';
        } else {
            return 'normal';
        }
    }

    /**
     * İşleme süresini hesapla
     */
    private function calculateProcessingTime($order, $oldStatus, $newStatus): ?int
    {
        // Bu bilgiyi order history'den hesaplayabiliriz
        // Şimdilik null döndürüyoruz
        return null;
    }

    /**
     * Retry mekanizması
     */
    private function scheduleRetry(OrderStatusChanged $event): void
    {
        Queue::later(now()->addMinutes(5), 'retry-order-status-changed', [
            'event' => serialize($event),
            'attempt' => 1,
        ]);
    }
}
