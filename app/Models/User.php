<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Kullanıcının siparişleri
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Kullanıcının sepeti
     */
    public function cart()
    {
        return $this->hasOne(Cart::class);
    }

    /**
     * Kullanıcının adresleri
     */
    public function addresses()
    {
        return $this->hasMany(UserAddress::class);
    }

    /**
     * Kullanıcının varsayılan adresi
     */
    public function defaultAddress()
    {
        return $this->addresses()->where('is_default', true)->first();
    }

    /**
     * Kullanıcının fatura adresleri
     */
    public function billingAddresses()
    {
        return $this->addresses()->where('is_billing', true);
    }

    /**
     * Kullanıcının varsayılan fatura adresi
     */
    public function defaultBillingAddress()
    {
        return $this->addresses()->where('is_billing', true)->where('is_default', true)->first();
    }

    /**
     * Kullanıcının favori ürünleri
     */
    public function favorites()
    {
        return $this->belongsToMany(Product::class, 'favorites')
                    ->withTimestamps();
    }

    /**
     * Kullanıcının görüntülediği ürünler
     */
    public function viewedProducts()
    {
        return $this->hasMany(ProductView::class);
    }

    /**
     * Kullanıcının son görüntülediği ürünleri getir
     */
    public function getRecentlyViewedProducts($limit = 10)
    {
        return $this->viewedProducts()
                    ->with('product')
                    ->orderBy('created_at', 'desc')
                    ->take($limit)
                    ->get()
                    ->pluck('product')
                    ->unique('id');
    }
}
