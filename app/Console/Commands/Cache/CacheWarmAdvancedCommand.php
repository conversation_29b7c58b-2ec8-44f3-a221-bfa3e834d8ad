<?php

namespace App\Console\Commands\Cache;

use App\Core\Infrastructure\Cache\Services\IntelligentCacheWarmer;
use App\Core\Infrastructure\Cache\Contracts\CacheAnalyticsInterface;
use App\Jobs\CacheWarmingJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;

/**
 * Advanced Cache Warming Command
 * Gelişmiş cache warming komutu
 */
class CacheWarmAdvancedCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cache:warm-advanced 
                            {type=all : Cache warming türü (all, product, category, user, order, cart, inventory, shipping, notification)}
                            {--intelligent : Akıllı cache warming kullan}
                            {--predictive : Öngörülü cache warming kullan}
                            {--priority=5 : Cache warming önceliği (1-10)}
                            {--batch-size=100 : Batch boyutu}
                            {--background : Background job olarak çalıştır}
                            {--dry-run : Sadece analiz yap, cache warming yapma}
                            {--force : <PERSON><PERSON><PERSON> (cache durumu kontrol etme)}
                            {--parallel=1 : <PERSON>lel işlem sayısı}
                            {--timeout=300 : Timeout süresi (saniye)}';

    /**
     * The console command description.
     */
    protected $description = 'Gelişmiş cache warming işlemi gerçekleştirir';

    protected IntelligentCacheWarmer $warmer;
    protected CacheAnalyticsInterface $analytics;

    /**
     * Create a new command instance.
     */
    public function __construct(IntelligentCacheWarmer $warmer, CacheAnalyticsInterface $analytics)
    {
        parent::__construct();
        $this->warmer = $warmer;
        $this->analytics = $analytics;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->argument('type');
        $options = $this->getOptions();

        $this->info("🔥 Gelişmiş Cache Warming Başlatılıyor...");
        $this->newLine();

        // Dry run kontrolü
        if ($options['dry_run']) {
            return $this->handleDryRun($type, $options);
        }

        // Background job kontrolü
        if ($options['background']) {
            return $this->handleBackgroundWarming($type, $options);
        }

        // Senkron warming
        return $this->handleSyncWarming($type, $options);
    }

    /**
     * Dry run işlemi
     */
    protected function handleDryRun(string $type, array $options): int
    {
        $this->warn("🔍 DRY RUN MODE - Sadece analiz yapılıyor, cache warming yapılmıyor");
        $this->newLine();

        try {
            if ($type === 'all') {
                $types = $this->getAllWarmingTypes();
                foreach ($types as $warmingType) {
                    $this->analyzeCacheWarming($warmingType, $options);
                }
            } else {
                $this->analyzeCacheWarming($type, $options);
            }

            $this->info("✅ Dry run tamamlandı");
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Dry run hatası: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Background warming işlemi
     */
    protected function handleBackgroundWarming(string $type, array $options): int
    {
        $this->info("🚀 Background job'lar kuyruğa ekleniyor...");

        try {
            $jobCount = 0;

            if ($type === 'all') {
                $types = $this->getAllWarmingTypes();
                foreach ($types as $warmingType) {
                    $this->dispatchWarmingJob($warmingType, $options);
                    $jobCount++;
                }
            } else {
                $this->dispatchWarmingJob($type, $options);
                $jobCount++;
            }

            $this->info("✅ {$jobCount} adet cache warming job'ı kuyruğa eklendi");
            $this->info("📊 Job durumunu takip etmek için: php artisan queue:monitor");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Background job hatası: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Senkron warming işlemi
     */
    protected function handleSyncWarming(string $type, array $options): int
    {
        $startTime = microtime(true);
        $totalWarmed = 0;
        $errors = [];

        try {
            if ($type === 'all') {
                $types = $this->getAllWarmingTypes();
                $progressBar = $this->output->createProgressBar(count($types));
                $progressBar->setFormat('verbose');

                foreach ($types as $warmingType) {
                    $result = $this->performWarming($warmingType, $options);
                    $totalWarmed += $result['items_warmed'] ?? 0;
                    
                    if (!empty($result['errors'])) {
                        $errors = array_merge($errors, $result['errors']);
                    }

                    $progressBar->advance();
                }

                $progressBar->finish();
                $this->newLine(2);
            } else {
                $result = $this->performWarming($type, $options);
                $totalWarmed = $result['items_warmed'] ?? 0;
                $errors = $result['errors'] ?? [];
            }

            $duration = microtime(true) - $startTime;

            // Sonuçları göster
            $this->displayResults($totalWarmed, $duration, $errors);

            return empty($errors) ? Command::SUCCESS : Command::FAILURE;

        } catch (\Exception $e) {
            $this->error("❌ Cache warming hatası: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Cache warming analizi
     */
    protected function analyzeCacheWarming(string $type, array $options): void
    {
        $this->info("🔍 {$type} cache warming analizi:");

        // Mevcut cache durumu
        $stats = $this->analytics->getStatistics($type);
        $this->line("  📊 Mevcut hit rate: " . round(($stats['hit_rate'] ?? 0) * 100, 2) . "%");
        $this->line("  🔑 Toplam key sayısı: " . ($stats['total_keys'] ?? 0));

        // Warming tahmini
        $estimate = $this->warmer->estimateWarmingTime($type, $options);
        $this->line("  ⏱️  Tahmini süre: " . $estimate['estimated_duration'] . " saniye");
        $this->line("  📦 Warming edilecek item sayısı: " . $estimate['estimated_items']);

        $this->newLine();
    }

    /**
     * Warming job'ını dispatch et
     */
    protected function dispatchWarmingJob(string $type, array $options): void
    {
        $priority = (int) $options['priority'];
        
        CacheWarmingJob::dispatch($type, $options, $priority)
            ->onQueue($this->getQueueByPriority($priority));

        $this->line("  ✅ {$type} warming job'ı kuyruğa eklendi (öncelik: {$priority})");
    }

    /**
     * Warming işlemini gerçekleştir
     */
    protected function performWarming(string $type, array $options): array
    {
        $this->info("🔥 {$type} cache warming başlatılıyor...");

        if ($options['intelligent']) {
            return $this->warmer->warmIntelligently($type, $options);
        } elseif ($options['predictive']) {
            return $this->warmer->warmPredictively($type, $options);
        } else {
            return $this->warmer->warmEntityType($type, $options);
        }
    }

    /**
     * Sonuçları göster
     */
    protected function displayResults(int $totalWarmed, float $duration, array $errors): void
    {
        $this->newLine();
        $this->info("📊 Cache Warming Sonuçları:");
        $this->line("  🔥 Toplam warming edilen item: {$totalWarmed}");
        $this->line("  ⏱️  Toplam süre: " . round($duration, 2) . " saniye");
        $this->line("  🚀 Saniyede item: " . round($totalWarmed / max($duration, 1), 2));

        if (!empty($errors)) {
            $this->newLine();
            $this->warn("⚠️  Hatalar (" . count($errors) . " adet):");
            foreach (array_slice($errors, 0, 5) as $error) {
                $this->line("  • " . $error);
            }
            
            if (count($errors) > 5) {
                $this->line("  • ... ve " . (count($errors) - 5) . " hata daha");
            }
        }
    }

    /**
     * Komut seçeneklerini al
     */
    protected function getOptions(): array
    {
        return [
            'intelligent' => $this->option('intelligent'),
            'predictive' => $this->option('predictive'),
            'priority' => (int) $this->option('priority'),
            'batch_size' => (int) $this->option('batch-size'),
            'background' => $this->option('background'),
            'dry_run' => $this->option('dry-run'),
            'force' => $this->option('force'),
            'parallel' => (int) $this->option('parallel'),
            'timeout' => (int) $this->option('timeout'),
        ];
    }

    /**
     * Tüm warming türlerini al
     */
    protected function getAllWarmingTypes(): array
    {
        return ['product', 'category', 'user', 'order', 'cart', 'inventory', 'shipping', 'notification'];
    }

    /**
     * Priority'ye göre queue adını al
     */
    protected function getQueueByPriority(int $priority): string
    {
        if ($priority <= 2) {
            return 'cache-high';
        } elseif ($priority <= 5) {
            return 'cache-medium';
        } else {
            return 'cache-low';
        }
    }
}
