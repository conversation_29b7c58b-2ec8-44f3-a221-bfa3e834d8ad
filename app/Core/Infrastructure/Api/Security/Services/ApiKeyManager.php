<?php

namespace App\Core\Infrastructure\Api\Security\Services;

use App\Core\Infrastructure\Api\Security\Contracts\ApiKeyManagerInterface;
use App\Core\Infrastructure\Api\Security\Models\ApiKey;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * ApiKeyManager
 * API key management service
 */
class ApiKeyManager implements ApiKeyManagerInterface
{
    private array $config;

    public function __construct()
    {
        // Config helper'ı mevcut değilse default de<PERSON><PERSON>ler kullan
        try {
            $this->config = config('api_rate_limiting.api_keys', []);
        } catch (\Exception $e) {
            $this->config = $this->getDefaultConfig();
        }
    }

    /**
     * Default config'i al
     */
    private function getDefaultConfig(): array
    {
        return [
            'key_prefix' => 'ak_',
            'expiration_days' => 365,
        ];
    }

    /**
     * Yeni API key oluştur
     */
    public function createApiKey(array $data): array
    {
        $key = ApiKey::generateKey($this->config['key_prefix'] ?? 'ak_');
        $secret = ApiKey::generateSecret();

        $apiKey = ApiKey::create([
            'name' => $data['name'],
            'key' => $key,
            'secret' => $secret,
            'user_id' => $data['user_id'] ?? null,
            'permissions' => $data['permissions'] ?? null,
            'rate_limit_per_minute' => $data['rate_limit_per_minute'] ?? null,
            'rate_limit_per_hour' => $data['rate_limit_per_hour'] ?? null,
            'rate_limit_per_day' => $data['rate_limit_per_day'] ?? null,
            'allowed_ips' => $data['allowed_ips'] ?? null,
            'allowed_domains' => $data['allowed_domains'] ?? null,
            'expires_at' => $data['expires_at'] ?? now()->addDays($this->config['expiration_days'] ?? 365),
            'metadata' => $data['metadata'] ?? null,
        ]);

        // Cache'e ekle
        $this->cacheApiKey($apiKey);

        return [
            'id' => $apiKey->id,
            'name' => $apiKey->name,
            'key' => $apiKey->key,
            'secret' => $apiKey->secret,
            'expires_at' => $apiKey->expires_at,
            'created_at' => $apiKey->created_at,
        ];
    }

    /**
     * API key'i doğrula
     */
    public function validateApiKey(string $key): ?array
    {
        // Cache'den kontrol et
        $cacheKey = 'api_key:' . $key;
        $cached = Cache::get($cacheKey);

        if ($cached) {
            return $cached;
        }

        // Database'den al
        $apiKey = ApiKey::where('key', $key)->active()->first();

        if (!$apiKey) {
            return null;
        }

        // IP kontrolü
        $currentIp = request()->ip();
        if (!$apiKey->isIpAllowed($currentIp)) {
            return null;
        }

        // Domain kontrolü
        $domain = request()->getHost();
        if (!$apiKey->isDomainAllowed($domain)) {
            return null;
        }

        $keyData = [
            'id' => $apiKey->id,
            'name' => $apiKey->name,
            'user_id' => $apiKey->user_id,
            'permissions' => $apiKey->permissions,
            'rate_limits' => $apiKey->getRateLimits(),
            'expires_at' => $apiKey->expires_at,
        ];

        // Cache'e kaydet
        Cache::put($cacheKey, $keyData, 300); // 5 dakika cache

        return $keyData;
    }

    /**
     * API key'i devre dışı bırak
     */
    public function revokeApiKey(string $key): bool
    {
        $apiKey = ApiKey::where('key', $key)->first();

        if (!$apiKey) {
            return false;
        }

        $apiKey->update(['is_active' => false]);

        // Cache'den sil
        Cache::forget('api_key:' . $key);

        return true;
    }

    /**
     * API key'i yenile
     */
    public function rotateApiKey(string $key): array
    {
        $apiKey = ApiKey::where('key', $key)->first();

        if (!$apiKey) {
            throw new \Exception('API key not found');
        }

        $newKey = ApiKey::generateKey($this->config['key_prefix'] ?? 'ak_');
        $newSecret = ApiKey::generateSecret();

        // Eski key'i devre dışı bırak
        $apiKey->update(['is_active' => false]);

        // Yeni key oluştur
        $newApiKey = $apiKey->replicate();
        $newApiKey->key = $newKey;
        $newApiKey->secret = $newSecret;
        $newApiKey->is_active = true;
        $newApiKey->usage_count = 0;
        $newApiKey->last_used_at = null;
        $newApiKey->save();

        // Cache'leri güncelle
        Cache::forget('api_key:' . $key);
        $this->cacheApiKey($newApiKey);

        return [
            'id' => $newApiKey->id,
            'name' => $newApiKey->name,
            'key' => $newApiKey->key,
            'secret' => $newApiKey->secret,
            'expires_at' => $newApiKey->expires_at,
            'created_at' => $newApiKey->created_at,
        ];
    }

    /**
     * API key kullanımını kaydet
     */
    public function recordUsage(string $key, array $usage): void
    {
        $apiKey = ApiKey::where('key', $key)->first();

        if ($apiKey) {
            $apiKey->recordUsage($usage);
        }
    }

    /**
     * API key'in rate limit'ini al
     */
    public function getApiKeyLimits(string $key): array
    {
        $apiKey = ApiKey::where('key', $key)->first();

        if (!$apiKey) {
            return [];
        }

        return $apiKey->getRateLimits();
    }

    /**
     * API key'in kullanım istatistiklerini al
     */
    public function getUsageStatistics(string $key): array
    {
        $apiKey = ApiKey::where('key', $key)->first();

        if (!$apiKey) {
            return [];
        }

        return $apiKey->getUsageStatistics();
    }

    /**
     * Süresi dolan API key'leri temizle
     */
    public function cleanupExpiredKeys(): int
    {
        $expiredKeys = ApiKey::expired()->get();
        $count = $expiredKeys->count();

        foreach ($expiredKeys as $apiKey) {
            // Cache'den sil
            Cache::forget('api_key:' . $apiKey->key);

            // Soft delete
            $apiKey->delete();
        }

        return $count;
    }

    /**
     * Tüm API key'leri listele
     */
    public function listApiKeys(array $filters = []): array
    {
        $query = ApiKey::query();

        // Filters
        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['expired'])) {
            if ($filters['expired']) {
                $query->expired();
            } else {
                $query->active();
            }
        }

        $apiKeys = $query->orderBy('created_at', 'desc')->get();

        return $apiKeys->map(function ($apiKey) {
            return [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'key' => $apiKey->key,
                'user_id' => $apiKey->user_id,
                'is_active' => $apiKey->is_active,
                'usage_count' => $apiKey->usage_count,
                'last_used_at' => $apiKey->last_used_at,
                'expires_at' => $apiKey->expires_at,
                'created_at' => $apiKey->created_at,
            ];
        })->toArray();
    }

    /**
     * API key'in detaylarını al
     */
    public function getApiKeyDetails(string $key): ?array
    {
        $apiKey = ApiKey::where('key', $key)->first();

        if (!$apiKey) {
            return null;
        }

        return [
            'id' => $apiKey->id,
            'name' => $apiKey->name,
            'key' => $apiKey->key,
            'user_id' => $apiKey->user_id,
            'permissions' => $apiKey->permissions,
            'rate_limits' => $apiKey->getRateLimits(),
            'allowed_ips' => $apiKey->allowed_ips,
            'allowed_domains' => $apiKey->allowed_domains,
            'is_active' => $apiKey->is_active,
            'usage_count' => $apiKey->usage_count,
            'last_used_at' => $apiKey->last_used_at,
            'expires_at' => $apiKey->expires_at,
            'created_at' => $apiKey->created_at,
            'updated_at' => $apiKey->updated_at,
            'metadata' => $apiKey->metadata,
            'usage_statistics' => $apiKey->getUsageStatistics(),
        ];
    }

    /**
     * API key'i cache'e kaydet
     */
    private function cacheApiKey(ApiKey $apiKey): void
    {
        $cacheKey = 'api_key:' . $apiKey->key;
        $keyData = [
            'id' => $apiKey->id,
            'name' => $apiKey->name,
            'user_id' => $apiKey->user_id,
            'permissions' => $apiKey->permissions,
            'rate_limits' => $apiKey->getRateLimits(),
            'expires_at' => $apiKey->expires_at,
        ];

        Cache::put($cacheKey, $keyData, 300); // 5 dakika cache
    }
}
