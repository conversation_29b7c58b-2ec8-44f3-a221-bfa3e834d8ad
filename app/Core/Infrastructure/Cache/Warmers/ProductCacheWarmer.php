<?php

namespace App\Core\Infrastructure\Cache\Warmers;

use App\Core\Infrastructure\Cache\Contracts\CacheWarmerInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheKeyGeneratorInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheTagManagerInterface;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Product Cache Warmer
 * Ürün cache'lerini ısıtan servis
 */
class ProductCacheWarmer implements CacheWarmerInterface
{
    protected ProductRepositoryInterface $productRepository;
    protected CacheKeyGeneratorInterface $keyGenerator;
    protected CacheTagManagerInterface $tagManager;
    protected array $config;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        CacheKeyGeneratorInterface $keyGenerator,
        CacheTagManagerInterface $tagManager,
        array $config = []
    ) {
        $this->productRepository = $productRepository;
        $this->keyGenerator = $keyGenerator;
        $this->tagManager = $tagManager;
        $this->config = array_merge([
            'enabled' => true,
            'priority' => 2,
            'batch_size' => 50,
            'chunk_size' => 100,
            'timeout' => 300,
            'warm_featured' => true,
            'warm_popular' => true,
            'warm_new' => true,
            'warm_categories' => true,
        ], $config);
    }

    /**
     * Cache'i ısıt
     */
    public function warm(array $options = []): array
    {
        $startTime = microtime(true);
        $warmedItems = 0;
        $errors = [];

        try {
            Log::info('Starting product cache warming');

            // Öne çıkan ürünleri ısıt
            if ($this->config['warm_featured']) {
                $result = $this->warmFeaturedProducts($options);
                $warmedItems += $result['count'];
                if (!empty($result['errors'])) {
                    $errors = array_merge($errors, $result['errors']);
                }
            }

            // Popüler ürünleri ısıt
            if ($this->config['warm_popular']) {
                $result = $this->warmPopularProducts($options);
                $warmedItems += $result['count'];
                if (!empty($result['errors'])) {
                    $errors = array_merge($errors, $result['errors']);
                }
            }

            // Yeni ürünleri ısıt
            if ($this->config['warm_new']) {
                $result = $this->warmNewProducts($options);
                $warmedItems += $result['count'];
                if (!empty($result['errors'])) {
                    $errors = array_merge($errors, $result['errors']);
                }
            }

            // Kategori bazlı ürünleri ısıt
            if ($this->config['warm_categories']) {
                $result = $this->warmProductsByCategories($options);
                $warmedItems += $result['count'];
                if (!empty($result['errors'])) {
                    $errors = array_merge($errors, $result['errors']);
                }
            }

            // Öngörülü ısıtma
            if (isset($options['predictions'])) {
                $result = $this->warmPredictedProducts($options['predictions']);
                $warmedItems += $result['count'];
                if (!empty($result['errors'])) {
                    $errors = array_merge($errors, $result['errors']);
                }
            }

            $duration = microtime(true) - $startTime;

            Log::info('Product cache warming completed', [
                'items_warmed' => $warmedItems,
                'duration' => $duration,
                'errors_count' => count($errors),
            ]);

            return [
                'status' => 'success',
                'type' => $this->getType(),
                'items_warmed' => $warmedItems,
                'duration' => $duration,
                'errors' => $errors,
                'memory_used' => memory_get_usage(true),
            ];

        } catch (\Exception $e) {
            Log::error('Product cache warming failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'status' => 'error',
                'type' => $this->getType(),
                'message' => $e->getMessage(),
                'items_warmed' => $warmedItems,
                'duration' => microtime(true) - $startTime,
            ];
        }
    }

    /**
     * Cache ısıtma önceliğini al
     */
    public function getPriority(): int
    {
        return $this->config['priority'];
    }

    /**
     * Cache ısıtma türünü al
     */
    public function getType(): string
    {
        return 'product';
    }

    /**
     * Cache ısıtmanın etkin olup olmadığını kontrol et
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'] && config('cache_infrastructure.entities.product.warming_enabled', true);
    }

    /**
     * Tahmini ısıtma süresini al
     */
    public function getEstimatedDuration(): int
    {
        $itemCount = $this->getItemCount();
        $batchSize = $this->config['batch_size'];
        $estimatedTimePerBatch = 2; // saniye

        return (int) ceil($itemCount / $batchSize) * $estimatedTimePerBatch;
    }

    /**
     * Isıtılacak öğe sayısını al
     */
    public function getItemCount(): int
    {
        try {
            $count = 0;

            if ($this->config['warm_featured']) {
                $count += $this->productRepository->getFeaturedProducts()->count();
            }

            if ($this->config['warm_popular']) {
                $count += min(100, $this->productRepository->getPopularProducts(100)->count());
            }

            if ($this->config['warm_new']) {
                $count += min(50, $this->productRepository->getNewProducts(50)->count());
            }

            return $count;

        } catch (\Exception $e) {
            Log::warning('Failed to get product count for warming', [
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * Cache ısıtma durumunu al
     */
    public function getStatus(): array
    {
        return [
            'type' => $this->getType(),
            'enabled' => $this->isEnabled(),
            'priority' => $this->getPriority(),
            'estimated_duration' => $this->getEstimatedDuration(),
            'estimated_items' => $this->getItemCount(),
            'config' => $this->config,
        ];
    }

    /**
     * Öne çıkan ürünleri ısıt
     */
    protected function warmFeaturedProducts(array $options): array
    {
        $warmedCount = 0;
        $errors = [];

        try {
            $featuredProducts = $this->productRepository->getFeaturedProducts();

            foreach ($featuredProducts->chunk($this->config['chunk_size']) as $products) {
                foreach ($products as $product) {
                    try {
                        $this->warmSingleProduct($product);
                        $warmedCount++;
                    } catch (\Exception $e) {
                        $errors[] = "Failed to warm featured product {$product->getId()}: " . $e->getMessage();
                    }
                }
            }

            // Öne çıkan ürünler listesini de cache'le
            $key = $this->keyGenerator->generateListKey('product', 'featured');
            $tags = $this->tagManager->getEntityTags('product', 'featured');
            Cache::tags($tags)->put($key, $featuredProducts, 3600);

        } catch (\Exception $e) {
            $errors[] = "Failed to warm featured products: " . $e->getMessage();
        }

        return ['count' => $warmedCount, 'errors' => $errors];
    }

    /**
     * Popüler ürünleri ısıt
     */
    protected function warmPopularProducts(array $options): array
    {
        $warmedCount = 0;
        $errors = [];

        try {
            $popularProducts = $this->productRepository->getPopularProducts(100);

            foreach ($popularProducts->chunk($this->config['chunk_size']) as $products) {
                foreach ($products as $product) {
                    try {
                        $this->warmSingleProduct($product);
                        $warmedCount++;
                    } catch (\Exception $e) {
                        $errors[] = "Failed to warm popular product {$product->getId()}: " . $e->getMessage();
                    }
                }
            }

            // Popüler ürünler listesini de cache'le
            $key = $this->keyGenerator->generateListKey('product', 'popular');
            $tags = $this->tagManager->getEntityTags('product', 'popular');
            Cache::tags($tags)->put($key, $popularProducts, 1800);

        } catch (\Exception $e) {
            $errors[] = "Failed to warm popular products: " . $e->getMessage();
        }

        return ['count' => $warmedCount, 'errors' => $errors];
    }

    /**
     * Yeni ürünleri ısıt
     */
    protected function warmNewProducts(array $options): array
    {
        $warmedCount = 0;
        $errors = [];

        try {
            $newProducts = $this->productRepository->getNewProducts(50);

            foreach ($newProducts->chunk($this->config['chunk_size']) as $products) {
                foreach ($products as $product) {
                    try {
                        $this->warmSingleProduct($product);
                        $warmedCount++;
                    } catch (\Exception $e) {
                        $errors[] = "Failed to warm new product {$product->getId()}: " . $e->getMessage();
                    }
                }
            }

            // Yeni ürünler listesini de cache'le
            $key = $this->keyGenerator->generateListKey('product', 'new');
            $tags = $this->tagManager->getEntityTags('product', 'new');
            Cache::tags($tags)->put($key, $newProducts, 1800);

        } catch (\Exception $e) {
            $errors[] = "Failed to warm new products: " . $e->getMessage();
        }

        return ['count' => $warmedCount, 'errors' => $errors];
    }

    /**
     * Kategori bazlı ürünleri ısıt
     */
    protected function warmProductsByCategories(array $options): array
    {
        $warmedCount = 0;
        $errors = [];

        try {
            // Ana kategorileri al
            $mainCategories = $this->productRepository->getMainCategories();

            foreach ($mainCategories as $category) {
                try {
                    $categoryProducts = $this->productRepository->getProductsByCategory($category->getId(), 20);
                    
                    foreach ($categoryProducts as $product) {
                        try {
                            $this->warmSingleProduct($product);
                            $warmedCount++;
                        } catch (\Exception $e) {
                            $errors[] = "Failed to warm category product {$product->getId()}: " . $e->getMessage();
                        }
                    }

                    // Kategori ürün listesini cache'le
                    $key = $this->keyGenerator->generateListKey('product', "category_{$category->getId()}");
                    $tags = $this->tagManager->getEntityTags('category', $category->getId());
                    Cache::tags($tags)->put($key, $categoryProducts, 1800);

                } catch (\Exception $e) {
                    $errors[] = "Failed to warm products for category {$category->getId()}: " . $e->getMessage();
                }
            }

        } catch (\Exception $e) {
            $errors[] = "Failed to warm products by categories: " . $e->getMessage();
        }

        return ['count' => $warmedCount, 'errors' => $errors];
    }

    /**
     * Öngörülü ürünleri ısıt
     */
    protected function warmPredictedProducts(array $predictions): array
    {
        $warmedCount = 0;
        $errors = [];

        try {
            // Yüksek talep gören anahtarları ısıt
            if (isset($predictions['high_demand_keys'])) {
                foreach ($predictions['high_demand_keys'] as $keyData) {
                    try {
                        $key = $keyData['key'];
                        
                        // Anahtar product ile ilgiliyse ısıt
                        if (strpos($key, 'product_') === 0) {
                            $productId = str_replace('product_', '', $key);
                            $product = $this->productRepository->findById($productId);
                            
                            if ($product) {
                                $this->warmSingleProduct($product);
                                $warmedCount++;
                            }
                        }
                    } catch (\Exception $e) {
                        $errors[] = "Failed to warm predicted product key {$keyData['key']}: " . $e->getMessage();
                    }
                }
            }

            // Gelecekteki anahtarları ısıt
            if (isset($predictions['predicted_keys']['next_hour'])) {
                foreach ($predictions['predicted_keys']['next_hour'] as $key) {
                    try {
                        if (strpos($key, 'product_') === 0) {
                            $productId = str_replace('product_', '', $key);
                            $product = $this->productRepository->findById($productId);
                            
                            if ($product) {
                                $this->warmSingleProduct($product);
                                $warmedCount++;
                            }
                        }
                    } catch (\Exception $e) {
                        $errors[] = "Failed to warm predicted future product key {$key}: " . $e->getMessage();
                    }
                }
            }

        } catch (\Exception $e) {
            $errors[] = "Failed to warm predicted products: " . $e->getMessage();
        }

        return ['count' => $warmedCount, 'errors' => $errors];
    }

    /**
     * Tek bir ürünü ısıt
     */
    protected function warmSingleProduct($product): void
    {
        $productId = $product->getId();

        // Ana ürün bilgisi
        $entityKey = $this->keyGenerator->generateEntityKey('product', $productId);
        $entityTags = $this->tagManager->getEntityTags('product', $productId);
        Cache::tags($entityTags)->put($entityKey, $product, 3600);

        // Ürün detayları (ilişkilerle birlikte)
        $detailKey = $this->keyGenerator->generateKey('product', 'detail', $productId);
        $productWithRelations = $this->productRepository->findByIdWithRelations($productId);
        Cache::tags($entityTags)->put($detailKey, $productWithRelations, 3600);

        // Ürün varyantları
        if (method_exists($product, 'getVariants')) {
            $variantsKey = $this->keyGenerator->generateKey('product', 'variants', $productId);
            $variants = $product->getVariants();
            Cache::tags($entityTags)->put($variantsKey, $variants, 1800);
        }

        // Ürün fiyatı
        if (method_exists($product, 'getPrice')) {
            $priceKey = $this->keyGenerator->generateKey('product', 'price', $productId);
            $price = $product->getPrice();
            Cache::tags($entityTags)->put($priceKey, $price, 900);
        }

        // Ürün stok durumu
        if (method_exists($product, 'getStock')) {
            $stockKey = $this->keyGenerator->generateKey('product', 'stock', $productId);
            $stock = $product->getStock();
            Cache::tags($entityTags)->put($stockKey, $stock, 300);
        }
    }
}
