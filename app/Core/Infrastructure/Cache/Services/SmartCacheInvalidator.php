<?php

namespace App\Core\Infrastructure\Cache\Services;

use App\Core\Infrastructure\Cache\Contracts\CacheInvalidatorInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheTagManagerInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheAnalyticsInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

/**
 * Smart Cache Invalidator
 * Akıllı cache geçersizleştirme servisi
 */
class SmartCacheInvalidator implements CacheInvalidatorInterface
{
    protected CacheTagManagerInterface $tagManager;
    protected CacheAnalyticsInterface $analytics;
    protected array $config;
    protected array $invalidationHistory = [];
    protected string $currentStrategy = 'immediate';

    // Geçersizleştirme stratejileri
    protected array $strategies = [
        'immediate' => ['delay' => 0, 'batch_size' => 1],
        'delayed' => ['delay' => 300, 'batch_size' => 10],
        'cascade' => ['delay' => 0, 'batch_size' => 50, 'max_depth' => 3],
        'selective' => ['delay' => 60, 'batch_size' => 5],
        'batch' => ['delay' => 0, 'batch_size' => 100],
    ];

    public function __construct(
        CacheTagManagerInterface $tagManager,
        CacheAnalyticsInterface $analytics,
        array $config = []
    ) {
        $this->tagManager = $tagManager;
        $this->analytics = $analytics;
        $this->config = array_merge([
            'max_cascade_depth' => 3,
            'batch_size' => 50,
            'delay_threshold' => 100,
            'selective_threshold' => 0.8,
            'history_limit' => 1000,
        ], $config);
    }

    /**
     * Cache'i geçersizleştir
     */
    public function invalidate($keys, array $options = []): bool
    {
        $keys = is_array($keys) ? $keys : [$keys];
        $strategy = $options['strategy'] ?? $this->currentStrategy;

        try {
            $result = $this->executeInvalidationStrategy($keys, $strategy, $options);
            
            $this->recordInvalidation([
                'keys' => $keys,
                'strategy' => $strategy,
                'status' => $result ? 'success' : 'failed',
                'timestamp' => now(),
                'options' => $options,
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Cache invalidation failed', [
                'keys' => $keys,
                'strategy' => $strategy,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Tag'lere göre cache'i geçersizleştir
     */
    public function invalidateByTags($tags, array $options = []): bool
    {
        $tags = is_array($tags) ? $tags : [$tags];

        try {
            // Tag'lere bağlı anahtarları al
            $keys = $this->getKeysByTags($tags);
            
            if (empty($keys)) {
                return true; // Geçersizleştirilecek anahtar yok
            }

            // Akıllı geçersizleştirme stratejisi seç
            $strategy = $this->selectOptimalStrategy($keys, $options);
            
            return $this->invalidate($keys, array_merge($options, ['strategy' => $strategy]));

        } catch (\Exception $e) {
            Log::error('Tag-based cache invalidation failed', [
                'tags' => $tags,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Pattern'e göre cache'i geçersizleştir
     */
    public function invalidateByPattern(string $pattern, array $options = []): bool
    {
        try {
            $keys = $this->getKeysByPattern($pattern);
            
            if (empty($keys)) {
                return true;
            }

            $strategy = $this->selectOptimalStrategy($keys, $options);
            
            return $this->invalidate($keys, array_merge($options, ['strategy' => $strategy]));

        } catch (\Exception $e) {
            Log::error('Pattern-based cache invalidation failed', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Basamaklı geçersizleştirme
     */
    public function cascadeInvalidate(string $entityType, $entityId, array $options = []): bool
    {
        $maxDepth = $options['max_depth'] ?? $this->config['max_cascade_depth'];
        $visited = [];

        return $this->performCascadeInvalidation($entityType, $entityId, 0, $maxDepth, $visited);
    }

    /**
     * Toplu geçersizleştirme
     */
    public function batchInvalidate(array $items, array $options = []): array
    {
        $batchSize = $options['batch_size'] ?? $this->config['batch_size'];
        $results = [];
        $batches = array_chunk($items, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            try {
                $batchKeys = [];
                
                foreach ($batch as $item) {
                    if (is_string($item)) {
                        $batchKeys[] = $item;
                    } elseif (is_array($item) && isset($item['key'])) {
                        $batchKeys[] = $item['key'];
                    }
                }

                $success = $this->invalidate($batchKeys, $options);
                
                $results[] = [
                    'batch' => $batchIndex + 1,
                    'keys_count' => count($batchKeys),
                    'status' => $success ? 'success' : 'failed',
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'batch' => $batchIndex + 1,
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Geçersizleştirme stratejisini ayarla
     */
    public function setStrategy(string $strategy): self
    {
        if (!isset($this->strategies[$strategy])) {
            throw new \InvalidArgumentException("Invalid invalidation strategy: {$strategy}");
        }

        $this->currentStrategy = $strategy;
        return $this;
    }

    /**
     * Geçersizleştirme geçmişini al
     */
    public function getInvalidationHistory(int $limit = 100): array
    {
        return array_slice($this->invalidationHistory, -$limit);
    }

    /**
     * Geçersizleştirme stratejisini çalıştır
     */
    protected function executeInvalidationStrategy(array $keys, string $strategy, array $options): bool
    {
        $strategyConfig = $this->strategies[$strategy];

        switch ($strategy) {
            case 'immediate':
                return $this->immediateInvalidation($keys);

            case 'delayed':
                return $this->delayedInvalidation($keys, $strategyConfig['delay']);

            case 'cascade':
                return $this->cascadeInvalidationByKeys($keys, $strategyConfig);

            case 'selective':
                return $this->selectiveInvalidation($keys, $options);

            case 'batch':
                return $this->batchInvalidationByKeys($keys, $strategyConfig['batch_size']);

            default:
                return $this->immediateInvalidation($keys);
        }
    }

    /**
     * Hemen geçersizleştirme
     */
    protected function immediateInvalidation(array $keys): bool
    {
        foreach ($keys as $key) {
            Cache::forget($key);
        }

        $this->analytics->recordEvent('cache_invalidation', [
            'type' => 'immediate',
            'keys_count' => count($keys),
            'timestamp' => now(),
        ]);

        return true;
    }

    /**
     * Gecikmeli geçersizleştirme
     */
    protected function delayedInvalidation(array $keys, int $delay): bool
    {
        // Gerçek uygulamada bu bir job olarak queue'ya eklenecek
        // Şimdilik basit bir implementasyon
        
        if ($delay > 0) {
            sleep($delay);
        }

        return $this->immediateInvalidation($keys);
    }

    /**
     * Seçici geçersizleştirme
     */
    protected function selectiveInvalidation(array $keys, array $options): bool
    {
        $threshold = $options['threshold'] ?? $this->config['selective_threshold'];
        $selectedKeys = [];

        foreach ($keys as $key) {
            // Cache hit rate'e göre seçici geçersizleştirme
            $hitRate = $this->getCacheHitRate($key);
            
            if ($hitRate < $threshold) {
                $selectedKeys[] = $key;
            }
        }

        return $this->immediateInvalidation($selectedKeys);
    }

    /**
     * Toplu geçersizleştirme (anahtar bazlı)
     */
    protected function batchInvalidationByKeys(array $keys, int $batchSize): bool
    {
        $batches = array_chunk($keys, $batchSize);
        
        foreach ($batches as $batch) {
            $this->immediateInvalidation($batch);
        }

        return true;
    }

    /**
     * Basamaklı geçersizleştirme işlemi
     */
    protected function performCascadeInvalidation(
        string $entityType, 
        $entityId, 
        int $currentDepth, 
        int $maxDepth, 
        array &$visited
    ): bool {
        if ($currentDepth >= $maxDepth) {
            return true;
        }

        $entityKey = "{$entityType}:{$entityId}";
        
        if (in_array($entityKey, $visited)) {
            return true; // Döngüsel referansı önle
        }

        $visited[] = $entityKey;

        // Entity'nin cache anahtarlarını al
        $entityKeys = $this->tagManager->getEntityTags($entityType, $entityId);
        
        // Entity cache'ini geçersizleştir
        $this->immediateInvalidation($entityKeys);

        // İlişkili entity'leri bul ve onları da geçersizleştir
        $relatedEntities = $this->getRelatedEntities($entityType, $entityId);
        
        foreach ($relatedEntities as $relatedEntity) {
            $this->performCascadeInvalidation(
                $relatedEntity['type'],
                $relatedEntity['id'],
                $currentDepth + 1,
                $maxDepth,
                $visited
            );
        }

        return true;
    }

    /**
     * Optimal stratejiyi seç
     */
    protected function selectOptimalStrategy(array $keys, array $options): string
    {
        $keyCount = count($keys);

        // Anahtar sayısına göre strateji seçimi
        if ($keyCount === 1) {
            return 'immediate';
        } elseif ($keyCount <= 10) {
            return 'selective';
        } elseif ($keyCount <= 50) {
            return 'delayed';
        } else {
            return 'batch';
        }
    }

    /**
     * Tag'lere göre anahtarları al
     */
    protected function getKeysByTags(array $tags): array
    {
        $keys = [];
        
        foreach ($tags as $tag) {
            $tagKeys = Cache::tags($tag)->getKeys() ?? [];
            $keys = array_merge($keys, $tagKeys);
        }

        return array_unique($keys);
    }

    /**
     * Pattern'e göre anahtarları al
     */
    protected function getKeysByPattern(string $pattern): array
    {
        try {
            // Redis kullanıyorsak KEYS komutu ile pattern matching
            if (config('cache.default') === 'redis') {
                return Redis::keys($pattern);
            }

            // Diğer cache driver'ları için basit implementasyon
            return [];

        } catch (\Exception $e) {
            Log::warning('Pattern key search failed', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Cache hit rate'i al
     */
    protected function getCacheHitRate(string $key): float
    {
        // Analytics servisinden hit rate bilgisini al
        $stats = $this->analytics->getStatistics();
        
        return $stats['keys'][$key]['hit_rate'] ?? 0.0;
    }

    /**
     * İlişkili entity'leri al
     */
    protected function getRelatedEntities(string $entityType, $entityId): array
    {
        // Bu method entity türüne göre özelleştirilmeli
        // Şimdilik basit bir implementasyon
        
        $relations = [
            'product' => ['category', 'inventory'],
            'category' => ['product'],
            'order' => ['user', 'product', 'payment'],
            'user' => ['order', 'cart'],
        ];

        return $relations[$entityType] ?? [];
    }

    /**
     * Geçersizleştirme kaydını tut
     */
    protected function recordInvalidation(array $data): void
    {
        $this->invalidationHistory[] = $data;

        // Geçmiş limitini kontrol et
        if (count($this->invalidationHistory) > $this->config['history_limit']) {
            $this->invalidationHistory = array_slice(
                $this->invalidationHistory, 
                -$this->config['history_limit']
            );
        }

        // Analytics'e kaydet
        $this->analytics->recordEvent('cache_invalidation', $data);
    }
}
