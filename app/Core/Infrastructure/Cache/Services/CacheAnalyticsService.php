<?php

namespace App\Core\Infrastructure\Cache\Services;

use App\Core\Infrastructure\Cache\Contracts\CacheAnalyticsInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * Cache Analytics Service
 * Cache analitik ve istatistik servisi
 */
class CacheAnalyticsService implements CacheAnalyticsInterface
{
    protected array $config;
    protected array $events = [];
    protected array $alerts = [];

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'storage_driver' => 'database', // database, redis, file
            'retention_days' => 30,
            'alert_thresholds' => [
                'hit_rate_low' => 0.7,
                'response_time_high' => 1000, // ms
                'memory_usage_high' => 0.8,
                'error_rate_high' => 0.05,
            ],
            'sampling_rate' => 1.0, // 1.0 = %100, 0.1 = %10
        ], $config);
    }

    /**
     * Cache istatistiklerini al
     */
    public function getStatistics(?string $entityType = null, array $options = []): array
    {
        try {
            $period = $options['period'] ?? 'day';
            $startDate = $this->getStartDate($period);

            $baseStats = [
                'period' => $period,
                'start_date' => $startDate,
                'end_date' => now(),
                'total_requests' => 0,
                'cache_hits' => 0,
                'cache_misses' => 0,
                'hit_rate' => 0.0,
                'avg_response_time' => 0.0,
                'total_keys' => 0,
                'memory_usage' => 0,
                'error_count' => 0,
            ];

            if ($entityType) {
                return $this->getEntityStatistics($entityType, $baseStats, $options);
            }

            return $this->getGlobalStatistics($baseStats, $options);

        } catch (\Exception $e) {
            Log::error('Failed to get cache statistics', [
                'entity_type' => $entityType,
                'error' => $e->getMessage(),
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Cache performans metriklerini al
     */
    public function getPerformanceMetrics(string $period = 'day', array $options = []): array
    {
        try {
            $startDate = $this->getStartDate($period);
            
            return [
                'response_times' => $this->getResponseTimeMetrics($startDate, $period),
                'throughput' => $this->getThroughputMetrics($startDate, $period),
                'hit_rates' => $this->getHitRateMetrics($startDate, $period),
                'memory_usage' => $this->getMemoryUsageMetrics($startDate, $period),
                'error_rates' => $this->getErrorRateMetrics($startDate, $period),
                'top_keys' => $this->getTopKeys(10, $period),
                'slow_keys' => $this->getSlowKeys(10, $period),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get performance metrics', [
                'period' => $period,
                'error' => $e->getMessage(),
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Cache hit/miss oranlarını al
     */
    public function getHitMissRatio(?string $entityType = null, string $period = 'day'): array
    {
        try {
            $startDate = $this->getStartDate($period);
            $events = $this->getEvents(['cache_hit', 'cache_miss'], $startDate, $entityType);

            $hits = 0;
            $misses = 0;
            $hourlyData = [];

            foreach ($events as $event) {
                $hour = Carbon::parse($event['timestamp'])->format('H:00');
                
                if (!isset($hourlyData[$hour])) {
                    $hourlyData[$hour] = ['hits' => 0, 'misses' => 0];
                }

                if ($event['event'] === 'cache_hit') {
                    $hits++;
                    $hourlyData[$hour]['hits']++;
                } else {
                    $misses++;
                    $hourlyData[$hour]['misses']++;
                }
            }

            $total = $hits + $misses;
            $hitRate = $total > 0 ? $hits / $total : 0;

            return [
                'total_hits' => $hits,
                'total_misses' => $misses,
                'hit_rate' => $hitRate,
                'miss_rate' => 1 - $hitRate,
                'hourly_breakdown' => $hourlyData,
                'period' => $period,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get hit/miss ratio', [
                'entity_type' => $entityType,
                'period' => $period,
                'error' => $e->getMessage(),
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * En çok kullanılan cache anahtarlarını al
     */
    public function getTopKeys(int $limit = 10, string $period = 'day'): array
    {
        try {
            $startDate = $this->getStartDate($period);
            $events = $this->getEvents(['cache_hit', 'cache_miss'], $startDate);

            $keyStats = [];

            foreach ($events as $event) {
                $key = $event['data']['key'] ?? 'unknown';
                
                if (!isset($keyStats[$key])) {
                    $keyStats[$key] = [
                        'key' => $key,
                        'hits' => 0,
                        'misses' => 0,
                        'total_requests' => 0,
                        'response_times' => [],
                    ];
                }

                $keyStats[$key]['total_requests']++;
                
                if ($event['event'] === 'cache_hit') {
                    $keyStats[$key]['hits']++;
                } else {
                    $keyStats[$key]['misses']++;
                }

                if (isset($event['data']['response_time'])) {
                    $keyStats[$key]['response_times'][] = $event['data']['response_time'];
                }
            }

            // Hit rate ve ortalama response time hesapla
            foreach ($keyStats as &$stats) {
                $total = $stats['hits'] + $stats['misses'];
                $stats['hit_rate'] = $total > 0 ? $stats['hits'] / $total : 0;
                $stats['avg_response_time'] = !empty($stats['response_times']) 
                    ? array_sum($stats['response_times']) / count($stats['response_times']) 
                    : 0;
                unset($stats['response_times']); // Gereksiz veriyi temizle
            }

            // En çok kullanılanlara göre sırala
            uasort($keyStats, fn($a, $b) => $b['total_requests'] <=> $a['total_requests']);

            return array_slice(array_values($keyStats), 0, $limit);

        } catch (\Exception $e) {
            Log::error('Failed to get top keys', [
                'limit' => $limit,
                'period' => $period,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Cache boyut analizi
     */
    public function getSizeAnalysis(?string $entityType = null): array
    {
        try {
            $analysis = [
                'total_size' => 0,
                'key_count' => 0,
                'avg_key_size' => 0,
                'largest_keys' => [],
                'size_distribution' => [],
                'by_entity_type' => [],
            ];

            // Redis kullanıyorsak detaylı analiz
            if (config('cache.default') === 'redis') {
                $analysis = array_merge($analysis, $this->getRedisAnalysis($entityType));
            }

            // Database cache kullanıyorsak
            if (config('cache.default') === 'database') {
                $analysis = array_merge($analysis, $this->getDatabaseAnalysis($entityType));
            }

            return $analysis;

        } catch (\Exception $e) {
            Log::error('Failed to get size analysis', [
                'entity_type' => $entityType,
                'error' => $e->getMessage(),
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Cache sağlık durumunu kontrol et
     */
    public function getHealthStatus(): array
    {
        try {
            $health = [
                'overall_status' => 'healthy',
                'checks' => [],
                'alerts' => $this->getAlerts(),
                'recommendations' => [],
            ];

            // Temel sağlık kontrolleri
            $health['checks']['connectivity'] = $this->checkConnectivity();
            $health['checks']['hit_rate'] = $this->checkHitRate();
            $health['checks']['response_time'] = $this->checkResponseTime();
            $health['checks']['memory_usage'] = $this->checkMemoryUsage();
            $health['checks']['error_rate'] = $this->checkErrorRate();

            // Genel durumu belirle
            $failedChecks = array_filter($health['checks'], fn($check) => $check['status'] !== 'healthy');
            
            if (count($failedChecks) > 0) {
                $health['overall_status'] = count($failedChecks) > 2 ? 'critical' : 'warning';
            }

            // Öneriler oluştur
            $health['recommendations'] = $this->generateRecommendations($health['checks']);

            return $health;

        } catch (\Exception $e) {
            Log::error('Failed to get health status', [
                'error' => $e->getMessage(),
            ]);

            return [
                'overall_status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Cache trend analizi
     */
    public function getTrendAnalysis(string $metric, string $period = 'week'): array
    {
        try {
            $startDate = $this->getStartDate($period);
            $data = [];

            switch ($metric) {
                case 'hits':
                    $data = $this->getHitTrend($startDate, $period);
                    break;
                case 'misses':
                    $data = $this->getMissTrend($startDate, $period);
                    break;
                case 'response_time':
                    $data = $this->getResponseTimeTrend($startDate, $period);
                    break;
                case 'memory':
                    $data = $this->getMemoryTrend($startDate, $period);
                    break;
                default:
                    throw new \InvalidArgumentException("Unknown metric: {$metric}");
            }

            return [
                'metric' => $metric,
                'period' => $period,
                'data' => $data,
                'trend' => $this->calculateTrend($data),
                'forecast' => $this->generateForecast($data),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get trend analysis', [
                'metric' => $metric,
                'period' => $period,
                'error' => $e->getMessage(),
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Cache raporu oluştur
     */
    public function generateReport(string $type = 'summary', array $options = []): array
    {
        try {
            $period = $options['period'] ?? 'day';
            
            switch ($type) {
                case 'summary':
                    return $this->generateSummaryReport($period, $options);
                case 'detailed':
                    return $this->generateDetailedReport($period, $options);
                case 'performance':
                    return $this->generatePerformanceReport($period, $options);
                default:
                    throw new \InvalidArgumentException("Unknown report type: {$type}");
            }

        } catch (\Exception $e) {
            Log::error('Failed to generate report', [
                'type' => $type,
                'error' => $e->getMessage(),
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Cache olaylarını kaydet
     */
    public function recordEvent(string $event, array $data = []): bool
    {
        try {
            // Sampling kontrolü
            if (mt_rand() / mt_getrandmax() > $this->config['sampling_rate']) {
                return true; // Skip this event
            }

            $eventData = [
                'event' => $event,
                'data' => $data,
                'timestamp' => now(),
                'memory_usage' => memory_get_usage(true),
            ];

            // Storage driver'a göre kaydet
            switch ($this->config['storage_driver']) {
                case 'database':
                    return $this->recordEventToDatabase($eventData);
                case 'redis':
                    return $this->recordEventToRedis($eventData);
                case 'file':
                    return $this->recordEventToFile($eventData);
                default:
                    $this->events[] = $eventData;
                    return true;
            }

        } catch (\Exception $e) {
            Log::error('Failed to record cache event', [
                'event' => $event,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Cache uyarılarını al
     */
    public function getAlerts(): array
    {
        return $this->alerts;
    }

    /**
     * Global istatistikleri al
     */
    protected function getGlobalStatistics(array $baseStats, array $options): array
    {
        $events = $this->getEvents([], $this->getStartDate($options['period'] ?? 'day'));
        
        $hits = 0;
        $misses = 0;
        $responseTimes = [];
        $errors = 0;

        foreach ($events as $event) {
            switch ($event['event']) {
                case 'cache_hit':
                    $hits++;
                    if (isset($event['data']['response_time'])) {
                        $responseTimes[] = $event['data']['response_time'];
                    }
                    break;
                case 'cache_miss':
                    $misses++;
                    break;
                case 'cache_error':
                    $errors++;
                    break;
            }
        }

        $total = $hits + $misses;
        
        return array_merge($baseStats, [
            'total_requests' => $total,
            'cache_hits' => $hits,
            'cache_misses' => $misses,
            'hit_rate' => $total > 0 ? $hits / $total : 0,
            'avg_response_time' => !empty($responseTimes) ? array_sum($responseTimes) / count($responseTimes) : 0,
            'error_count' => $errors,
        ]);
    }

    /**
     * Entity istatistiklerini al
     */
    protected function getEntityStatistics(string $entityType, array $baseStats, array $options): array
    {
        $events = $this->getEvents([], $this->getStartDate($options['period'] ?? 'day'), $entityType);
        
        // Entity-specific statistics logic
        return $this->getGlobalStatistics($baseStats, $options);
    }

    /**
     * Başlangıç tarihini al
     */
    protected function getStartDate(string $period): Carbon
    {
        switch ($period) {
            case 'hour':
                return now()->subHour();
            case 'day':
                return now()->subDay();
            case 'week':
                return now()->subWeek();
            case 'month':
                return now()->subMonth();
            default:
                return now()->subDay();
        }
    }

    /**
     * Olayları al
     */
    protected function getEvents(array $eventTypes = [], Carbon $startDate = null, string $entityType = null): array
    {
        switch ($this->config['storage_driver']) {
            case 'database':
                return $this->getEventsFromDatabase($eventTypes, $startDate, $entityType);
            case 'redis':
                return $this->getEventsFromRedis($eventTypes, $startDate, $entityType);
            case 'file':
                return $this->getEventsFromFile($eventTypes, $startDate, $entityType);
            default:
                return $this->events;
        }
    }

    /**
     * Database'den olayları al
     */
    protected function getEventsFromDatabase(array $eventTypes, Carbon $startDate = null, string $entityType = null): array
    {
        $query = DB::table('cache_events');

        if (!empty($eventTypes)) {
            $query->whereIn('event', $eventTypes);
        }

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        if ($entityType) {
            $query->where('entity_type', $entityType);
        }

        return $query->orderBy('created_at', 'desc')->get()->toArray();
    }

    /**
     * Redis'ten olayları al
     */
    protected function getEventsFromRedis(array $eventTypes, Carbon $startDate = null, string $entityType = null): array
    {
        // Redis implementation
        return [];
    }

    /**
     * Dosyadan olayları al
     */
    protected function getEventsFromFile(array $eventTypes, Carbon $startDate = null, string $entityType = null): array
    {
        // File implementation
        return [];
    }

    /**
     * Database'e olay kaydet
     */
    protected function recordEventToDatabase(array $eventData): bool
    {
        try {
            DB::table('cache_events')->insert([
                'event' => $eventData['event'],
                'data' => json_encode($eventData['data']),
                'memory_usage' => $eventData['memory_usage'],
                'created_at' => $eventData['timestamp'],
                'updated_at' => $eventData['timestamp'],
            ]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Redis'e olay kaydet
     */
    protected function recordEventToRedis(array $eventData): bool
    {
        // Redis implementation
        return true;
    }

    /**
     * Dosyaya olay kaydet
     */
    protected function recordEventToFile(array $eventData): bool
    {
        // File implementation
        return true;
    }

    /**
     * Bağlantı kontrolü
     */
    protected function checkConnectivity(): array
    {
        try {
            Cache::put('health_check', 'test', 60);
            $value = Cache::get('health_check');
            Cache::forget('health_check');

            return [
                'status' => $value === 'test' ? 'healthy' : 'warning',
                'message' => $value === 'test' ? 'Cache connectivity OK' : 'Cache connectivity issue',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'critical',
                'message' => 'Cache connectivity failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Hit rate kontrolü
     */
    protected function checkHitRate(): array
    {
        $stats = $this->getStatistics();
        $hitRate = $stats['hit_rate'] ?? 0;
        $threshold = $this->config['alert_thresholds']['hit_rate_low'];

        return [
            'status' => $hitRate >= $threshold ? 'healthy' : 'warning',
            'message' => "Hit rate: {$hitRate} (threshold: {$threshold})",
            'value' => $hitRate,
        ];
    }

    /**
     * Response time kontrolü
     */
    protected function checkResponseTime(): array
    {
        $stats = $this->getStatistics();
        $avgResponseTime = $stats['avg_response_time'] ?? 0;
        $threshold = $this->config['alert_thresholds']['response_time_high'];

        return [
            'status' => $avgResponseTime <= $threshold ? 'healthy' : 'warning',
            'message' => "Avg response time: {$avgResponseTime}ms (threshold: {$threshold}ms)",
            'value' => $avgResponseTime,
        ];
    }

    /**
     * Memory usage kontrolü
     */
    protected function checkMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = $this->convertToBytes($memoryLimit);
        $usageRatio = $memoryLimitBytes > 0 ? $memoryUsage / $memoryLimitBytes : 0;
        $threshold = $this->config['alert_thresholds']['memory_usage_high'];

        return [
            'status' => $usageRatio <= $threshold ? 'healthy' : 'warning',
            'message' => "Memory usage: " . round($usageRatio * 100, 2) . "% (threshold: " . round($threshold * 100, 2) . "%)",
            'value' => $usageRatio,
        ];
    }

    /**
     * Error rate kontrolü
     */
    protected function checkErrorRate(): array
    {
        $stats = $this->getStatistics();
        $errorCount = $stats['error_count'] ?? 0;
        $totalRequests = $stats['total_requests'] ?? 1;
        $errorRate = $errorCount / $totalRequests;
        $threshold = $this->config['alert_thresholds']['error_rate_high'];

        return [
            'status' => $errorRate <= $threshold ? 'healthy' : 'warning',
            'message' => "Error rate: " . round($errorRate * 100, 2) . "% (threshold: " . round($threshold * 100, 2) . "%)",
            'value' => $errorRate,
        ];
    }

    /**
     * Önerileri oluştur
     */
    protected function generateRecommendations(array $checks): array
    {
        $recommendations = [];

        foreach ($checks as $checkName => $check) {
            if ($check['status'] !== 'healthy') {
                switch ($checkName) {
                    case 'hit_rate':
                        $recommendations[] = 'Consider warming cache or reviewing cache keys';
                        break;
                    case 'response_time':
                        $recommendations[] = 'Optimize cache queries or increase cache TTL';
                        break;
                    case 'memory_usage':
                        $recommendations[] = 'Consider increasing memory limit or implementing cache eviction';
                        break;
                    case 'error_rate':
                        $recommendations[] = 'Review cache error logs and fix underlying issues';
                        break;
                }
            }
        }

        return $recommendations;
    }

    /**
     * Bytes'a çevir
     */
    protected function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Hit trend analizi
     */
    protected function getHitTrend(Carbon $startDate, string $period): array
    {
        $events = $this->getEvents(['cache_hit'], $startDate);
        return $this->groupEventsByTime($events, $period);
    }

    /**
     * Miss trend analizi
     */
    protected function getMissTrend(Carbon $startDate, string $period): array
    {
        $events = $this->getEvents(['cache_miss'], $startDate);
        return $this->groupEventsByTime($events, $period);
    }

    /**
     * Response time trend analizi
     */
    protected function getResponseTimeTrend(Carbon $startDate, string $period): array
    {
        $events = $this->getEvents(['cache_hit', 'cache_miss'], $startDate);
        $grouped = $this->groupEventsByTime($events, $period);

        foreach ($grouped as &$group) {
            $responseTimes = array_column($group['events'], 'response_time');
            $group['avg_response_time'] = !empty($responseTimes)
                ? array_sum($responseTimes) / count($responseTimes)
                : 0;
        }

        return $grouped;
    }

    /**
     * Memory trend analizi
     */
    protected function getMemoryTrend(Carbon $startDate, string $period): array
    {
        $events = $this->getEvents([], $startDate);
        $grouped = $this->groupEventsByTime($events, $period);

        foreach ($grouped as &$group) {
            $memoryUsages = array_column($group['events'], 'memory_usage');
            $group['avg_memory_usage'] = !empty($memoryUsages)
                ? array_sum($memoryUsages) / count($memoryUsages)
                : 0;
        }

        return $grouped;
    }

    /**
     * Trend hesaplama
     */
    protected function calculateTrend(array $data): array
    {
        if (count($data) < 2) {
            return ['direction' => 'stable', 'percentage' => 0];
        }

        $values = array_column($data, 'value');
        $first = reset($values);
        $last = end($values);

        if ($first == 0) {
            return ['direction' => 'stable', 'percentage' => 0];
        }

        $percentage = (($last - $first) / $first) * 100;
        $direction = $percentage > 5 ? 'increasing' : ($percentage < -5 ? 'decreasing' : 'stable');

        return [
            'direction' => $direction,
            'percentage' => round($percentage, 2),
            'first_value' => $first,
            'last_value' => $last,
        ];
    }

    /**
     * Forecast oluşturma
     */
    protected function generateForecast(array $data): array
    {
        if (count($data) < 3) {
            return ['forecast' => [], 'confidence' => 'low'];
        }

        $values = array_column($data, 'value');
        $trend = $this->calculateLinearTrend($values);

        // Basit linear forecast
        $forecast = [];
        $lastValue = end($values);

        for ($i = 1; $i <= 7; $i++) { // 7 günlük forecast
            $forecastValue = $lastValue + ($trend * $i);
            $forecast[] = [
                'period' => $i,
                'value' => max(0, $forecastValue), // Negatif değerleri engelle
            ];
        }

        return [
            'forecast' => $forecast,
            'confidence' => count($data) > 10 ? 'high' : 'medium',
            'trend_slope' => $trend,
        ];
    }

    /**
     * Summary raporu oluştur
     */
    protected function generateSummaryReport(string $period, array $options): array
    {
        $stats = $this->getStatistics($options['entity_type'] ?? null, ['period' => $period]);
        $health = $this->getHealthStatus();
        $topKeys = $this->getTopKeys(5, $period);

        return [
            'type' => 'summary',
            'period' => $period,
            'generated_at' => now()->toISOString(),
            'statistics' => $stats,
            'health' => $health,
            'top_keys' => $topKeys,
            'recommendations' => $this->generateRecommendations($health['checks'] ?? []),
        ];
    }

    /**
     * Detaylı rapor oluştur
     */
    protected function generateDetailedReport(string $period, array $options): array
    {
        $summaryReport = $this->generateSummaryReport($period, $options);
        $performance = $this->getPerformanceMetrics($period, $options);
        $trends = [
            'hits' => $this->getTrendAnalysis('hits', $period),
            'response_time' => $this->getTrendAnalysis('response_time', $period),
        ];

        return array_merge($summaryReport, [
            'type' => 'detailed',
            'performance' => $performance,
            'trends' => $trends,
            'size_analysis' => $this->getSizeAnalysis($options['entity_type'] ?? null),
        ]);
    }

    /**
     * Performance raporu oluştur
     */
    protected function generatePerformanceReport(string $period, array $options): array
    {
        $performance = $this->getPerformanceMetrics($period, $options);
        $slowKeys = $this->getSlowKeys(10, $period);

        return [
            'type' => 'performance',
            'period' => $period,
            'generated_at' => now()->toISOString(),
            'performance_metrics' => $performance,
            'slow_keys' => $slowKeys,
            'bottlenecks' => $this->identifyBottlenecks($performance),
            'optimization_suggestions' => $this->generateOptimizationSuggestions($performance),
        ];
    }

    /**
     * Eksik helper metodları
     */
    protected function getResponseTimeMetrics(Carbon $startDate, string $period): array
    {
        $events = $this->getEvents(['cache_hit', 'cache_miss'], $startDate);
        $responseTimes = array_column($events, 'response_time');

        if (empty($responseTimes)) {
            return ['avg' => 0, 'min' => 0, 'max' => 0, 'p95' => 0];
        }

        sort($responseTimes);
        $count = count($responseTimes);

        return [
            'avg' => array_sum($responseTimes) / $count,
            'min' => min($responseTimes),
            'max' => max($responseTimes),
            'p95' => $responseTimes[intval($count * 0.95)],
        ];
    }

    protected function getThroughputMetrics(Carbon $startDate, string $period): array
    {
        $events = $this->getEvents(['cache_hit', 'cache_miss'], $startDate);
        $grouped = $this->groupEventsByTime($events, 'hour');

        $throughputs = array_column($grouped, 'count');

        return [
            'avg_per_hour' => !empty($throughputs) ? array_sum($throughputs) / count($throughputs) : 0,
            'max_per_hour' => !empty($throughputs) ? max($throughputs) : 0,
            'total' => count($events),
        ];
    }

    protected function getHitRateMetrics(Carbon $startDate, string $period): array
    {
        return $this->getHitMissRatio(null, $period);
    }

    protected function getMemoryUsageMetrics(Carbon $startDate, string $period): array
    {
        $events = $this->getEvents([], $startDate);
        $memoryUsages = array_filter(array_column($events, 'memory_usage'));

        if (empty($memoryUsages)) {
            return ['avg' => 0, 'min' => 0, 'max' => 0];
        }

        return [
            'avg' => array_sum($memoryUsages) / count($memoryUsages),
            'min' => min($memoryUsages),
            'max' => max($memoryUsages),
        ];
    }

    protected function getErrorRateMetrics(Carbon $startDate, string $period): array
    {
        $totalEvents = $this->getEvents([], $startDate);
        $errorEvents = $this->getEvents(['cache_error'], $startDate);

        $total = count($totalEvents);
        $errors = count($errorEvents);

        return [
            'error_rate' => $total > 0 ? $errors / $total : 0,
            'total_errors' => $errors,
            'total_requests' => $total,
        ];
    }

    protected function getSlowKeys(int $limit, string $period): array
    {
        $startDate = $this->getStartDate($period);
        $events = $this->getEvents(['cache_hit', 'cache_miss'], $startDate);

        $keyResponseTimes = [];
        foreach ($events as $event) {
            $key = $event['cache_key'] ?? 'unknown';
            $responseTime = $event['response_time'] ?? 0;

            if (!isset($keyResponseTimes[$key])) {
                $keyResponseTimes[$key] = [];
            }
            $keyResponseTimes[$key][] = $responseTime;
        }

        $slowKeys = [];
        foreach ($keyResponseTimes as $key => $times) {
            $avgTime = array_sum($times) / count($times);
            $slowKeys[] = [
                'key' => $key,
                'avg_response_time' => $avgTime,
                'request_count' => count($times),
            ];
        }

        usort($slowKeys, fn($a, $b) => $b['avg_response_time'] <=> $a['avg_response_time']);

        return array_slice($slowKeys, 0, $limit);
    }

    protected function getRedisAnalysis(?string $entityType): array
    {
        try {
            $redis = Redis::connection();
            $info = $redis->info();

            return [
                'memory_usage' => $info['used_memory'] ?? 0,
                'key_count' => $redis->dbsize(),
                'hit_rate' => isset($info['keyspace_hits'], $info['keyspace_misses'])
                    ? $info['keyspace_hits'] / ($info['keyspace_hits'] + $info['keyspace_misses'])
                    : 0,
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    protected function getDatabaseAnalysis(?string $entityType): array
    {
        try {
            $query = DB::table('cache');

            if ($entityType) {
                $query->where('key', 'like', $entityType . '%');
            }

            return [
                'total_entries' => $query->count(),
                'total_size' => $query->sum(DB::raw('LENGTH(value)')),
                'expired_entries' => $query->where('expiration', '<', time())->count(),
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    protected function groupEventsByTime(array $events, string $period): array
    {
        $grouped = [];

        foreach ($events as $event) {
            $timestamp = Carbon::parse($event['created_at'] ?? $event['timestamp']);

            $key = match($period) {
                'hour' => $timestamp->format('Y-m-d H:00'),
                'day' => $timestamp->format('Y-m-d'),
                'week' => $timestamp->startOfWeek()->format('Y-m-d'),
                'month' => $timestamp->format('Y-m'),
                default => $timestamp->format('Y-m-d H:00'),
            };

            if (!isset($grouped[$key])) {
                $grouped[$key] = ['events' => [], 'count' => 0];
            }

            $grouped[$key]['events'][] = $event;
            $grouped[$key]['count']++;
        }

        return $grouped;
    }

    protected function calculateLinearTrend(array $values): float
    {
        $n = count($values);
        if ($n < 2) return 0;

        $sumX = array_sum(range(0, $n - 1));
        $sumY = array_sum($values);
        $sumXY = 0;
        $sumX2 = 0;

        for ($i = 0; $i < $n; $i++) {
            $sumXY += $i * $values[$i];
            $sumX2 += $i * $i;
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);

        return $slope;
    }

    protected function identifyBottlenecks(array $performance): array
    {
        $bottlenecks = [];

        if (isset($performance['response_times']['avg']) && $performance['response_times']['avg'] > 1000) {
            $bottlenecks[] = 'High average response time';
        }

        if (isset($performance['hit_rates']['hit_rate']) && $performance['hit_rates']['hit_rate'] < 0.7) {
            $bottlenecks[] = 'Low cache hit rate';
        }

        return $bottlenecks;
    }

    protected function generateOptimizationSuggestions(array $performance): array
    {
        $suggestions = [];

        if (isset($performance['response_times']['avg']) && $performance['response_times']['avg'] > 1000) {
            $suggestions[] = 'Consider implementing cache warming for frequently accessed keys';
            $suggestions[] = 'Review cache TTL settings to reduce cache misses';
        }

        if (isset($performance['hit_rates']['hit_rate']) && $performance['hit_rates']['hit_rate'] < 0.7) {
            $suggestions[] = 'Implement predictive caching strategies';
            $suggestions[] = 'Increase cache size or optimize eviction policies';
        }

        return $suggestions;
    }
}
