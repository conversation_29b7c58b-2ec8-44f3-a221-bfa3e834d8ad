<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Cache Infrastructure Configuration
    |--------------------------------------------------------------------------
    |
    | Bu dosya cache infrastructure'ının konfigürasyonunu içerir.
    |
    */

    'enabled' => env('CACHE_INFRASTRUCTURE_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Cache Metrics
    |--------------------------------------------------------------------------
    |
    | Cache performans metrikleri
    |
    */
    'metrics' => [
        'enabled' => env('CACHE_METRICS_ENABLED', false),
        'driver' => env('CACHE_METRICS_DRIVER', 'log'),
        'interval' => env('CACHE_METRICS_INTERVAL', 300), // 5 dakika
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Warming
    |--------------------------------------------------------------------------
    |
    | Cache warming konfigürasyonu
    |
    */
    'warming' => [
        'enabled' => env('CACHE_WARMING_ENABLED', true),
        'schedule' => env('CACHE_WARMING_SCHEDULE', '0 */6 * * *'), // Her 6 saatte bir
        'chunk_size' => env('CACHE_WARMING_CHUNK_SIZE', 100),
        'timeout' => env('CACHE_WARMING_TIMEOUT', 300), // 5 dakika
    ],

    /*
    |--------------------------------------------------------------------------
    | Entity-Specific Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Her entity için özel cache konfigürasyonu
    |
    */
    'entities' => [
        'product' => [
            'enabled' => env('CACHE_PRODUCT_ENABLED', true),
            'driver' => env('CACHE_PRODUCT_DRIVER', null),
            'warming_enabled' => env('CACHE_PRODUCT_WARMING_ENABLED', true),
            'warming_priority' => 2,
        ],
        'category' => [
            'enabled' => env('CACHE_CATEGORY_ENABLED', true),
            'driver' => env('CACHE_CATEGORY_DRIVER', null),
            'warming_enabled' => env('CACHE_CATEGORY_WARMING_ENABLED', true),
            'warming_priority' => 1,
        ],
        'cart' => [
            'enabled' => env('CACHE_CART_ENABLED', true),
            'driver' => env('CACHE_CART_DRIVER', null),
            'warming_enabled' => env('CACHE_CART_WARMING_ENABLED', false),
            'warming_priority' => 4,
        ],
        'payment' => [
            'enabled' => env('CACHE_PAYMENT_ENABLED', true),
            'driver' => env('CACHE_PAYMENT_DRIVER', null),
            'warming_enabled' => env('CACHE_PAYMENT_WARMING_ENABLED', false),
            'warming_priority' => 5,
        ],
        'order' => [
            'enabled' => env('CACHE_ORDER_ENABLED', true),
            'driver' => env('CACHE_ORDER_DRIVER', null),
            'warming_enabled' => env('CACHE_ORDER_WARMING_ENABLED', false),
            'warming_priority' => 3,
        ],
        'user' => [
            'enabled' => env('CACHE_USER_ENABLED', true),
            'driver' => env('CACHE_USER_DRIVER', null),
            'warming_enabled' => env('CACHE_USER_WARMING_ENABLED', true),
            'warming_priority' => 3,
        ],
        'inventory' => [
            'enabled' => env('CACHE_INVENTORY_ENABLED', true),
            'driver' => env('CACHE_INVENTORY_DRIVER', null),
            'warming_enabled' => env('CACHE_INVENTORY_WARMING_ENABLED', true),
            'warming_priority' => 2,
        ],
        'shipping' => [
            'enabled' => env('CACHE_SHIPPING_ENABLED', true),
            'driver' => env('CACHE_SHIPPING_DRIVER', null),
            'warming_enabled' => env('CACHE_SHIPPING_WARMING_ENABLED', true),
            'warming_priority' => 4,
        ],
        'notification' => [
            'enabled' => env('CACHE_NOTIFICATION_ENABLED', true),
            'driver' => env('CACHE_NOTIFICATION_DRIVER', null),
            'warming_enabled' => env('CACHE_NOTIFICATION_WARMING_ENABLED', true),
            'warming_priority' => 5,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Invalidation
    |--------------------------------------------------------------------------
    |
    | Cache invalidation stratejileri
    |
    */
    'invalidation' => [
        'strategies' => [
            'immediate' => [
                'delay' => 0,
                'batch_size' => 1,
            ],
            'delayed' => [
                'delay' => 300, // 5 dakika
                'batch_size' => 10,
            ],
            'cascade' => [
                'delay' => 0,
                'batch_size' => 50,
                'max_depth' => 3,
            ],
            'selective' => [
                'delay' => 60, // 1 dakika
                'batch_size' => 5,
                'threshold' => 0.8, // Hit rate threshold
            ],
            'batch' => [
                'delay' => 0,
                'batch_size' => 100,
                'timeout' => 300,
            ],
        ],
        'default_strategy' => env('CACHE_INVALIDATION_DEFAULT_STRATEGY', 'immediate'),
        'history_enabled' => env('CACHE_INVALIDATION_HISTORY_ENABLED', true),
        'history_limit' => env('CACHE_INVALIDATION_HISTORY_LIMIT', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Multi-Level Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Çok seviyeli cache konfigürasyonu
    |
    */
    'multi_level' => [
        'enabled' => env('CACHE_MULTI_LEVEL_ENABLED', true),
        'levels' => [
            1 => [
                'name' => 'L1_Memory',
                'driver' => 'array',
                'ttl' => env('CACHE_L1_TTL', 300), // 5 dakika
                'max_size' => env('CACHE_L1_MAX_SIZE', 1000),
                'enabled' => env('CACHE_L1_ENABLED', true),
            ],
            2 => [
                'name' => 'L2_Redis',
                'driver' => 'redis',
                'ttl' => env('CACHE_L2_TTL', 3600), // 1 saat
                'max_size' => env('CACHE_L2_MAX_SIZE', 10000),
                'enabled' => env('CACHE_L2_ENABLED', true),
            ],
            3 => [
                'name' => 'L3_Database',
                'driver' => 'database',
                'ttl' => env('CACHE_L3_TTL', 86400), // 24 saat
                'max_size' => env('CACHE_L3_MAX_SIZE', 100000),
                'enabled' => env('CACHE_L3_ENABLED', true),
            ],
        ],
        'sync_strategy' => env('CACHE_SYNC_STRATEGY', 'write_through'), // write_through, write_back, write_around
        'promotion_threshold' => env('CACHE_PROMOTION_THRESHOLD', 3),
        'eviction_policy' => env('CACHE_EVICTION_POLICY', 'lru'), // lru, lfu, fifo
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | Cache analitik konfigürasyonu
    |
    */
    'analytics' => [
        'enabled' => env('CACHE_ANALYTICS_ENABLED', true),
        'storage_driver' => env('CACHE_ANALYTICS_STORAGE', 'database'), // database, redis, file
        'retention_days' => env('CACHE_ANALYTICS_RETENTION_DAYS', 30),
        'sampling_rate' => env('CACHE_ANALYTICS_SAMPLING_RATE', 1.0), // 1.0 = %100
        'alert_thresholds' => [
            'hit_rate_low' => env('CACHE_ALERT_HIT_RATE_LOW', 0.7),
            'response_time_high' => env('CACHE_ALERT_RESPONSE_TIME_HIGH', 1000), // ms
            'memory_usage_high' => env('CACHE_ALERT_MEMORY_USAGE_HIGH', 0.8),
            'error_rate_high' => env('CACHE_ALERT_ERROR_RATE_HIGH', 0.05),
        ],
        'reports' => [
            'enabled' => env('CACHE_REPORTS_ENABLED', true),
            'schedule' => env('CACHE_REPORTS_SCHEDULE', '0 6 * * *'), // Daily at 6 AM
            'recipients' => env('CACHE_REPORTS_RECIPIENTS', ''),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Configuration
    |--------------------------------------------------------------------------
    |
    | Cache key generation konfigürasyonu
    |
    */
    'keys' => [
        'max_length' => env('CACHE_KEY_MAX_LENGTH', 250),
        'separator' => env('CACHE_KEY_SEPARATOR', ':'),
        'hash_algorithm' => env('CACHE_KEY_HASH_ALGORITHM', 'md5'),
        'normalize_keys' => env('CACHE_KEY_NORMALIZE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Tag Configuration
    |--------------------------------------------------------------------------
    |
    | Cache tag konfigürasyonu
    |
    */
    'tags' => [
        'enabled' => env('CACHE_TAGS_ENABLED', true),
        'max_tags_per_key' => env('CACHE_MAX_TAGS_PER_KEY', 10),
        'global_prefix' => env('CACHE_GLOBAL_TAG_PREFIX', 'global'),
        'hierarchy_enabled' => env('CACHE_TAG_HIERARCHY_ENABLED', true),
        'dependency_tracking' => env('CACHE_TAG_DEPENDENCY_TRACKING', true),
    ],
];
