<?php

namespace App\Core\Infrastructure\Cache\Contracts;

/**
 * Multi-Level Cache Interface
 * Çok seviyeli cache yönetimi için temel arayüz
 */
interface MultiLevelCacheInterface
{
    /**
     * Veriyi cache'den al (tüm seviyelerden)
     *
     * @param string $key Cache anahtarı
     * @param mixed $default Varsayılan değer
     * @return mixed
     */
    public function get(string $key, $default = null);

    /**
     * Veriyi tüm cache seviyelerine yaz
     *
     * @param string $key Cache anahtarı
     * @param mixed $value Değer
     * @param int|null $ttl TTL (saniye)
     * @param array $tags Cache tag'leri
     * @return bool
     */
    public function put(string $key, $value, ?int $ttl = null, array $tags = []): bool;

    /**
     * Veriyi tüm cache seviyelerinden sil
     *
     * @param string $key Cache anahtarı
     * @return bool
     */
    public function forget(string $key): bool;

    /**
     * Belirli bir seviyeden veri al
     *
     * @param int $level Cache seviyesi (1, 2, 3)
     * @param string $key Cache anahtarı
     * @param mixed $default Varsayılan değer
     * @return mixed
     */
    public function getFromLevel(int $level, string $key, $default = null);

    /**
     * Belirli bir seviyeye veri yaz
     *
     * @param int $level Cache seviyesi
     * @param string $key Cache anahtarı
     * @param mixed $value Değer
     * @param int|null $ttl TTL (saniye)
     * @param array $tags Cache tag'leri
     * @return bool
     */
    public function putToLevel(int $level, string $key, $value, ?int $ttl = null, array $tags = []): bool;

    /**
     * Cache seviyelerini senkronize et
     *
     * @param string $key Cache anahtarı
     * @return bool
     */
    public function synchronize(string $key): bool;

    /**
     * Cache hiyerarşisini optimize et
     *
     * @param array $options Optimizasyon seçenekleri
     * @return array Optimizasyon sonuçları
     */
    public function optimize(array $options = []): array;

    /**
     * Cache seviye istatistiklerini al
     *
     * @return array
     */
    public function getLevelStatistics(): array;

    /**
     * Cache seviyesi konfigürasyonunu al
     *
     * @param int $level Cache seviyesi
     * @return array
     */
    public function getLevelConfig(int $level): array;

    /**
     * Cache seviyesi durumunu kontrol et
     *
     * @param int $level Cache seviyesi
     * @return bool
     */
    public function isLevelHealthy(int $level): bool;

    /**
     * Cache seviyesini temizle
     *
     * @param int $level Cache seviyesi
     * @return bool
     */
    public function clearLevel(int $level): bool;
}
