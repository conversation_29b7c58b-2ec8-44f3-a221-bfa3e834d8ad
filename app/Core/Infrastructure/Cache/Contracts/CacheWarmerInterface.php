<?php

namespace App\Core\Infrastructure\Cache\Contracts;

/**
 * Cache Warmer Interface
 * Cache ısıtma servisleri için temel arayüz
 */
interface CacheWarmerInterface
{
    /**
     * Cache'i ısıt
     *
     * @param array $options Opsiyonel parametreler
     * @return array Sonuç bilgileri
     */
    public function warm(array $options = []): array;

    /**
     * Cache ısıtma önceliğini al
     *
     * @return int Öncelik değeri (düşük değer = yüksek öncelik)
     */
    public function getPriority(): int;

    /**
     * Cache ısıtma türünü al
     *
     * @return string Cache türü
     */
    public function getType(): string;

    /**
     * Cache ısıtmanın etkin olup olmadığını kontrol et
     *
     * @return bool
     */
    public function isEnabled(): bool;

    /**
     * Tahmini ısıtma süresini al (saniye)
     *
     * @return int
     */
    public function getEstimatedDuration(): int;

    /**
     * Isıtılacak öğe sayısını al
     *
     * @return int
     */
    public function getItemCount(): int;

    /**
     * Cache ısıtma durumunu al
     *
     * @return array
     */
    public function getStatus(): array;
}
