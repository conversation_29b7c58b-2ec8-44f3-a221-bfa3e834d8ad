<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API v1.0 Routes
|--------------------------------------------------------------------------
|
| API version 1.0 için route'lar
| Bu dosya /api/v1.0/ prefix'i ile yüklenir
|
*/

// Products API v1.0
Route::prefix('products')->name('products.')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\ProductController::class, 'index'])->name('index');
    Route::get('/{id}', [\App\Http\Controllers\Api\ProductController::class, 'show'])->name('show');
    Route::get('/{id}/details', [\App\Http\Controllers\Api\ProductController::class, 'getProductDetails'])->name('details');
    Route::get('/search', [\App\Http\Controllers\Api\ProductController::class, 'search'])->name('search');
});

// Categories API v1.0
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\CategoryController::class, 'index'])->name('index');
    Route::get('/tree', [\App\Http\Controllers\Api\CategoryController::class, 'tree'])->name('tree');
    Route::get('/{id}', [\App\Http\Controllers\Api\CategoryController::class, 'show'])->name('show');
});

// Location API v1.0
Route::prefix('locations')->name('locations.')->group(function () {
    Route::get('/countries', [\App\Http\Controllers\Api\LocationController::class, 'countries'])->name('countries');
    Route::get('/states/{countryId}', [\App\Http\Controllers\Api\LocationController::class, 'states'])->name('states');
    Route::get('/cities/{stateId}', [\App\Http\Controllers\Api\LocationController::class, 'cities'])->name('cities');
    Route::get('/turkey/cities/{stateId}', [\App\Http\Controllers\Api\LocationController::class, 'turkeyCities'])->name('turkey.cities');
});

// Utility API v1.0
Route::prefix('utils')->name('utils.')->group(function () {
    Route::post('/upload-image', [\App\Http\Controllers\Api\ImageUploadController::class, 'upload'])->name('upload.image');
});

// Cart API v1.0 (if available)
Route::prefix('cart')->name('cart.')->group(function () {
    Route::get('/count', [\App\Http\Controllers\CartController::class, 'getCartCount'])->name('count');
});

// Auth required routes
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Products management
    Route::prefix('products')->name('products.')->group(function () {
        Route::post('/', [\App\Http\Controllers\Api\ProductController::class, 'store'])->name('store');
        Route::put('/{id}', [\App\Http\Controllers\Api\ProductController::class, 'update'])->name('update');
        Route::delete('/{id}', [\App\Http\Controllers\Api\ProductController::class, 'destroy'])->name('destroy');
    });
    
    // Categories management
    Route::prefix('categories')->name('categories.')->group(function () {
        Route::post('/', [\App\Http\Controllers\Api\CategoryController::class, 'store'])->name('store');
        Route::put('/{id}', [\App\Http\Controllers\Api\CategoryController::class, 'update'])->name('update');
        Route::delete('/{id}', [\App\Http\Controllers\Api\CategoryController::class, 'destroy'])->name('destroy');
    });
    
    // Admin only routes
    Route::middleware(['role:admin'])->group(function () {
        Route::post('/locations/refresh-cache', [\App\Http\Controllers\Api\LocationController::class, 'refreshCache'])->name('locations.refresh');
    });
});
