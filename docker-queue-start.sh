#!/bin/bash

# Lara<PERSON> Queue Worker Docker Başlatma Script'i
# Bu script Docker container'larını queue worker'lar ile birlikte başlatır

set -e

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Log fonksiyonları
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Banner
echo "
╔══════════════════════════════════════════════════════════════╗
║                Laravel Queue Worker Docker                   ║
║                    Başlatma Script'i                         ║
╚══════════════════════════════════════════════════════════════╝
"

# Kullanım bilgisi
usage() {
    echo "Kullanım: $0 [SEÇENEKLER]"
    echo ""
    echo "Seçenekler:"
    echo "  --strategy [horizon|workers|both]  Queue worker stratejisi (varsayılan: horizon)"
    echo "  --profile [default|workers-only|horizon-only]  Docker Compose profili"
    echo "  --build                             Image'ları yeniden build et"
    echo "  --fresh                             Tüm volume'ları temizle ve fresh start"
    echo "  --migrate                           Otomatik migration çalıştır"
    echo "  --seed                              Database seed'leri çalıştır"
    echo "  --clear-cache                       Tüm cache'leri temizle"
    echo "  --logs                              Başlatma sonrası logları göster"
    echo "  --monitor                           Başlatma sonrası monitoring başlat"
    echo "  --help, -h                          Bu yardım mesajını göster"
    echo ""
    echo "Örnekler:"
    echo "  $0                                  # Varsayılan ayarlarla başlat"
    echo "  $0 --strategy workers --build       # Worker'lar ile build ederek başlat"
    echo "  $0 --profile horizon-only --migrate # Sadece Horizon ile migration'la başlat"
    echo "  $0 --fresh --seed                   # Fresh start ile seed'lerle başlat"
}

# Varsayılan değerler
STRATEGY="horizon"
PROFILE="default"
BUILD_FLAG=""
FRESH_START=false
AUTO_MIGRATE=false
RUN_SEEDS=false
CLEAR_CACHE=false
SHOW_LOGS=false
START_MONITOR=false

# Parametreleri parse et
while [[ $# -gt 0 ]]; do
    case $1 in
        --strategy)
            STRATEGY="$2"
            shift 2
            ;;
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --build)
            BUILD_FLAG="--build"
            shift
            ;;
        --fresh)
            FRESH_START=true
            shift
            ;;
        --migrate)
            AUTO_MIGRATE=true
            shift
            ;;
        --seed)
            RUN_SEEDS=true
            shift
            ;;
        --clear-cache)
            CLEAR_CACHE=true
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --monitor)
            START_MONITOR=true
            shift
            ;;
        --help|-h)
            usage
            exit 0
            ;;
        *)
            error "Bilinmeyen parametre: $1"
            usage
            exit 1
            ;;
    esac
done

# Strateji kontrolü
case $STRATEGY in
    horizon|workers|both)
        ;;
    *)
        error "Geçersiz strateji: $STRATEGY. Geçerli değerler: horizon, workers, both"
        exit 1
        ;;
esac

# Profil kontrolü
case $PROFILE in
    default|workers-only|horizon-only)
        ;;
    *)
        error "Geçersiz profil: $PROFILE. Geçerli değerler: default, workers-only, horizon-only"
        exit 1
        ;;
esac

# .env dosyası kontrolü
if [ ! -f .env ]; then
    if [ -f .env.docker.example ]; then
        warn ".env dosyası bulunamadı. .env.docker.example'dan kopyalanıyor..."
        cp .env.docker.example .env
        warn "Lütfen .env dosyasını düzenleyip APP_KEY ve diğer ayarları yapın!"
        read -p "Devam etmek için Enter'a basın..."
    else
        error ".env dosyası bulunamadı ve .env.docker.example da mevcut değil!"
        exit 1
    fi
fi

# Docker ve Docker Compose kontrolü
if ! command -v docker &> /dev/null; then
    error "Docker yüklü değil!"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    error "Docker Compose yüklü değil!"
    exit 1
fi

# Docker Compose komutunu belirle
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

log "Başlatma parametreleri:"
info "  Queue Strategy: $STRATEGY"
info "  Docker Profile: $PROFILE"
info "  Build Flag: ${BUILD_FLAG:-none}"
info "  Fresh Start: $FRESH_START"
info "  Auto Migrate: $AUTO_MIGRATE"
info "  Run Seeds: $RUN_SEEDS"
info "  Clear Cache: $CLEAR_CACHE"

# Fresh start
if [ "$FRESH_START" = true ]; then
    warn "Fresh start - Tüm container'lar ve volume'lar silinecek!"
    read -p "Devam etmek istiyor musunuz? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Container'lar durduruluyor ve siliniyor..."
        $DOCKER_COMPOSE down -v --remove-orphans
        
        log "Docker image'ları temizleniyor..."
        docker system prune -f
        
        log "Volume'lar siliniyor..."
        docker volume rm $(docker volume ls -q | grep modularecommerce) 2>/dev/null || true
    else
        log "Fresh start iptal edildi."
        exit 0
    fi
fi

# Environment değişkenlerini ayarla
export QUEUE_STRATEGY=$STRATEGY
export AUTO_MIGRATE=$AUTO_MIGRATE
export CLEAR_FAILED_JOBS=false

# Profil ayarları
PROFILE_FLAGS=""
if [ "$PROFILE" != "default" ]; then
    PROFILE_FLAGS="--profile $PROFILE"
fi

# Container'ları başlat
log "Docker container'ları başlatılıyor..."
if [ "$PROFILE" = "default" ]; then
    # Varsayılan profil - app, nginx, mysql, redis
    $DOCKER_COMPOSE up -d $BUILD_FLAG app nginx mysql redis
elif [ "$PROFILE" = "workers-only" ]; then
    # Sadece worker'lar
    $DOCKER_COMPOSE $PROFILE_FLAGS up -d $BUILD_FLAG queue-worker mysql redis
elif [ "$PROFILE" = "horizon-only" ]; then
    # Sadece Horizon
    $DOCKER_COMPOSE $PROFILE_FLAGS up -d $BUILD_FLAG horizon mysql redis
fi

# Container'ların başlamasını bekle
log "Container'ların başlaması bekleniyor..."
sleep 10

# Health check
log "Container sağlığı kontrol ediliyor..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if $DOCKER_COMPOSE ps | grep -q "healthy\|Up"; then
        log "Container'lar sağlıklı şekilde başladı!"
        break
    else
        warn "Container'lar henüz hazır değil. Deneme $attempt/$max_attempts"
        sleep 5
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    error "Container'lar başlatılamadı!"
    $DOCKER_COMPOSE logs
    exit 1
fi

# Cache temizleme
if [ "$CLEAR_CACHE" = true ]; then
    log "Cache temizleniyor..."
    if [ "$PROFILE" = "default" ]; then
        $DOCKER_COMPOSE exec app php artisan cache:clear
        $DOCKER_COMPOSE exec app php artisan config:clear
        $DOCKER_COMPOSE exec app php artisan route:clear
        $DOCKER_COMPOSE exec app php artisan view:clear
    fi
fi

# Migration'lar
if [ "$AUTO_MIGRATE" = true ]; then
    log "Database migration'ları çalıştırılıyor..."
    if [ "$PROFILE" = "default" ]; then
        $DOCKER_COMPOSE exec app php artisan migrate --force
    elif [ "$PROFILE" = "workers-only" ]; then
        $DOCKER_COMPOSE exec queue-worker php artisan migrate --force
    elif [ "$PROFILE" = "horizon-only" ]; then
        $DOCKER_COMPOSE exec horizon php artisan migrate --force
    fi
fi

# Seed'ler
if [ "$RUN_SEEDS" = true ]; then
    log "Database seed'leri çalıştırılıyor..."
    if [ "$PROFILE" = "default" ]; then
        $DOCKER_COMPOSE exec app php artisan db:seed
    elif [ "$PROFILE" = "workers-only" ]; then
        $DOCKER_COMPOSE exec queue-worker php artisan db:seed
    elif [ "$PROFILE" = "horizon-only" ]; then
        $DOCKER_COMPOSE exec horizon php artisan db:seed
    fi
fi

# Başarı mesajı
log "🎉 Laravel Queue Worker Docker başarıyla başlatıldı!"
echo ""
info "=== Erişim Bilgileri ==="
if [ "$PROFILE" = "default" ]; then
    info "Web Uygulaması: http://localhost"
    info "Horizon Dashboard: http://localhost/horizon"
fi
info "MySQL: localhost:3306"
info "Redis: localhost:6379"
echo ""

info "=== Yönetim Komutları ==="
info "Queue durumu: docker exec modularecommerce_app queue-manager status"
info "Queue logları: docker exec modularecommerce_app queue-manager logs"
info "Queue monitoring: docker exec modularecommerce_app queue-manager monitor"
info "Container logları: $DOCKER_COMPOSE logs -f"
echo ""

# Log'ları göster
if [ "$SHOW_LOGS" = true ]; then
    log "Container logları gösteriliyor..."
    $DOCKER_COMPOSE logs -f &
    LOG_PID=$!
fi

# Monitoring başlat
if [ "$START_MONITOR" = true ]; then
    log "Queue monitoring başlatılıyor..."
    sleep 2
    if [ "$PROFILE" = "default" ]; then
        docker exec -it modularecommerce_app queue-manager monitor
    elif [ "$PROFILE" = "workers-only" ]; then
        docker exec -it modularecommerce_queue_worker queue-manager monitor
    elif [ "$PROFILE" = "horizon-only" ]; then
        docker exec -it modularecommerce_horizon queue-manager monitor
    fi
fi

# Cleanup function
cleanup() {
    if [ ! -z "$LOG_PID" ]; then
        kill $LOG_PID 2>/dev/null || true
    fi
}

trap cleanup EXIT

log "Script tamamlandı. Container'lar arka planda çalışmaya devam ediyor."
