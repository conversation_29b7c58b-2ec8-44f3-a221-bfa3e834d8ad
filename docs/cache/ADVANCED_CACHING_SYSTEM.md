# Advanced Caching System - Phase 5B

Bu dokümantasyon, ModularEcommerce projesinin Phase 5B: Advanced Caching System implementasyonunu açıklar.

## Genel Bakış

Advanced Caching System, aşağıdaki ana bileşenleri içerir:

1. **Cache Warming Strategies** - Akıllı cache ısıtma stratejileri
2. **Intelligent Cache Invalidation** - Akıllı cache geçersizleştirme
3. **Multi-Level Caching** - Çok seviyeli cache yönetimi
4. **Cache Analytics** - Cache performans analitikleri

## Özellikler

### 1. Cache Warming Strategies

#### Intelligent Cache Warmer
- Öncelik tabanlı cache ısıtma
- Öngörülü cache ısıtma (predictive warming)
- Paralel işleme desteği
- Background job desteği

```php
use App\Core\Infrastructure\Cache\Services\IntelligentCacheWarmer;

$warmer = app(IntelligentCacheWarmer::class);

// Tüm cache'leri ısıt
$result = $warmer->warmAll([
    'predictive_enabled' => true,
    'priority_threshold' => 2,
]);

// Belirli türde cache'i ısıt
$result = $warmer->warmType('product', [
    'batch_size' => 100,
]);
```

#### Cache Warmer Interface
```php
interface CacheWarmerInterface
{
    public function warm(array $options = []): array;
    public function getPriority(): int;
    public function getType(): string;
    public function isEnabled(): bool;
    public function getEstimatedDuration(): int;
    public function getItemCount(): int;
    public function getStatus(): array;
}
```

### 2. Intelligent Cache Invalidation

#### Smart Cache Invalidator
- Çoklu geçersizleştirme stratejileri
- Basamaklı geçersizleştirme (cascade invalidation)
- Seçici geçersizleştirme (selective invalidation)
- Toplu geçersizleştirme (batch invalidation)

```php
use App\Core\Infrastructure\Cache\Contracts\CacheInvalidatorInterface;

$invalidator = app(CacheInvalidatorInterface::class);

// Hemen geçersizleştirme
$invalidator->setStrategy('immediate');
$invalidator->invalidate(['key1', 'key2']);

// Tag'lere göre geçersizleştirme
$invalidator->invalidateByTags(['product', 'category']);

// Pattern'e göre geçersizleştirme
$invalidator->invalidateByPattern('product_*');

// Basamaklı geçersizleştirme
$invalidator->cascadeInvalidate('product', 123);

// Toplu geçersizleştirme
$invalidator->batchInvalidate([
    ['key' => 'key1'],
    ['key' => 'key2'],
], ['batch_size' => 50]);
```

#### Geçersizleştirme Stratejileri
- **immediate**: Hemen geçersizleştir
- **delayed**: Gecikmeli geçersizleştirme
- **cascade**: Basamaklı geçersizleştirme
- **selective**: Seçici geçersizleştirme (hit rate'e göre)
- **batch**: Toplu geçersizleştirme

### 3. Multi-Level Caching

#### Çok Seviyeli Cache Yönetimi
- L1 Cache (Memory/Array)
- L2 Cache (Redis)
- L3 Cache (Database)

```php
use App\Core\Infrastructure\Cache\Contracts\MultiLevelCacheInterface;

$multiCache = app(MultiLevelCacheInterface::class);

// Tüm seviyelerden veri al
$value = $multiCache->get('key');

// Tüm seviyelere veri yaz
$multiCache->put('key', $value, 3600, ['tag1', 'tag2']);

// Belirli seviyeden veri al
$value = $multiCache->getFromLevel(1, 'key'); // L1'den al

// Belirli seviyeye veri yaz
$multiCache->putToLevel(2, 'key', $value, 1800); // L2'ye yaz

// Cache seviyelerini senkronize et
$multiCache->synchronize('key');

// Cache hiyerarşisini optimize et
$result = $multiCache->optimize();
```

#### Sync Stratejileri
- **write_through**: Tüm seviyelere eş zamanlı yazma
- **write_back**: Sadece L1'e yazma, diğerleri lazy
- **write_around**: L1'i atlayarak yazma

### 4. Cache Analytics

#### Performans İzleme ve Analitik
- Cache hit/miss oranları
- Yanıt süreleri
- Bellek kullanımı
- Trend analizi
- Sağlık kontrolü

```php
use App\Core\Infrastructure\Cache\Contracts\CacheAnalyticsInterface;

$analytics = app(CacheAnalyticsInterface::class);

// Genel istatistikler
$stats = $analytics->getStatistics();

// Performans metrikleri
$metrics = $analytics->getPerformanceMetrics('day');

// Hit/miss oranları
$ratios = $analytics->getHitMissRatio('product', 'week');

// En çok kullanılan anahtarlar
$topKeys = $analytics->getTopKeys(10, 'day');

// Boyut analizi
$sizeAnalysis = $analytics->getSizeAnalysis();

// Sağlık durumu
$health = $analytics->getHealthStatus();

// Trend analizi
$trends = $analytics->getTrendAnalysis('hits', 'week');

// Rapor oluşturma
$report = $analytics->generateReport('detailed', [
    'period' => 'day',
    'entity_type' => 'product',
]);
```

## Konfigürasyon

### Cache Infrastructure Konfigürasyonu

```php
// config/cache_infrastructure.php

return [
    // Multi-level cache
    'multi_level' => [
        'enabled' => true,
        'levels' => [
            1 => [
                'name' => 'L1_Memory',
                'driver' => 'array',
                'ttl' => 300,
                'max_size' => 1000,
                'enabled' => true,
            ],
            2 => [
                'name' => 'L2_Redis',
                'driver' => 'redis',
                'ttl' => 3600,
                'max_size' => 10000,
                'enabled' => true,
            ],
            3 => [
                'name' => 'L3_Database',
                'driver' => 'database',
                'ttl' => 86400,
                'max_size' => 100000,
                'enabled' => true,
            ],
        ],
        'sync_strategy' => 'write_through',
        'promotion_threshold' => 3,
        'eviction_policy' => 'lru',
    ],

    // Cache invalidation
    'invalidation' => [
        'strategies' => [
            'immediate' => ['delay' => 0, 'batch_size' => 1],
            'delayed' => ['delay' => 300, 'batch_size' => 10],
            'cascade' => ['delay' => 0, 'batch_size' => 50, 'max_depth' => 3],
            'selective' => ['delay' => 60, 'batch_size' => 5, 'threshold' => 0.8],
            'batch' => ['delay' => 0, 'batch_size' => 100, 'timeout' => 300],
        ],
        'default_strategy' => 'immediate',
        'history_enabled' => true,
        'history_limit' => 1000,
    ],

    // Cache analytics
    'analytics' => [
        'enabled' => true,
        'storage_driver' => 'database',
        'retention_days' => 30,
        'sampling_rate' => 1.0,
        'alert_thresholds' => [
            'hit_rate_low' => 0.7,
            'response_time_high' => 1000,
            'memory_usage_high' => 0.8,
            'error_rate_high' => 0.05,
        ],
    ],
];
```

### Environment Variables

```env
# Multi-level cache
CACHE_MULTI_LEVEL_ENABLED=true
CACHE_L1_ENABLED=true
CACHE_L1_TTL=300
CACHE_L2_ENABLED=true
CACHE_L2_TTL=3600
CACHE_L3_ENABLED=true
CACHE_L3_TTL=86400
CACHE_SYNC_STRATEGY=write_through

# Cache invalidation
CACHE_INVALIDATION_DEFAULT_STRATEGY=immediate
CACHE_INVALIDATION_HISTORY_ENABLED=true

# Cache analytics
CACHE_ANALYTICS_ENABLED=true
CACHE_ANALYTICS_STORAGE=database
CACHE_ANALYTICS_RETENTION_DAYS=30
CACHE_ANALYTICS_SAMPLING_RATE=1.0

# Alert thresholds
CACHE_ALERT_HIT_RATE_LOW=0.7
CACHE_ALERT_RESPONSE_TIME_HIGH=1000
CACHE_ALERT_MEMORY_USAGE_HIGH=0.8
CACHE_ALERT_ERROR_RATE_HIGH=0.05
```

## Komutlar

### Cache Warming
```bash
# Gelişmiş cache warming
php artisan cache:warm-advanced all --intelligent --predictive

# Belirli tür için warming
php artisan cache:warm-advanced product --priority=2

# Background job olarak çalıştır
php artisan cache:warm-advanced all --background

# Dry run (sadece analiz)
php artisan cache:warm-advanced all --dry-run
```

### Cache Analytics
```bash
# Özet rapor
php artisan cache:analytics --type=summary --period=day

# Detaylı rapor
php artisan cache:analytics --type=detailed --period=week

# Performans raporu
php artisan cache:analytics --type=performance --period=month

# Belirli entity için analiz
php artisan cache:analytics --entity=product --period=day

# Raporu dosyaya export et
php artisan cache:analytics --type=detailed --export=/tmp/cache_report.json
```

## Job'lar

### Cache Warming Job
```php
use App\Jobs\CacheWarmingJob;

// Background cache warming
CacheWarmingJob::dispatch('product', [
    'batch_size' => 100,
    'priority_threshold' => 2,
]);
```

## Migration

Cache events tablosu için migration:

```bash
php artisan migrate
```

## Test

```bash
# Unit testleri çalıştır
php artisan test tests/Unit/Cache/

# Belirli test sınıfı
php artisan test tests/Unit/Cache/IntelligentCacheWarmerTest.php

# Coverage ile
php artisan test --coverage
```

## Performans Optimizasyonları

1. **Cache Warming**: Yoğun saatlerde önceden cache'i ısıtın
2. **Multi-Level**: Sık kullanılan verileri L1'de tutun
3. **Selective Invalidation**: Düşük hit rate'li anahtarları öncelikle geçersizleştirin
4. **Analytics Sampling**: Yüksek trafikte sampling rate'i düşürün
5. **Background Jobs**: Ağır cache işlemlerini background'da çalıştırın

## Monitoring

Cache sağlığını izlemek için:

1. Hit rate'leri takip edin (>70% olmalı)
2. Yanıt sürelerini kontrol edin (<1000ms)
3. Bellek kullanımını izleyin (<80%)
4. Hata oranlarını takip edin (<5%)

## Troubleshooting

### Yaygın Sorunlar

1. **Düşük Hit Rate**: Cache warming stratejisini gözden geçirin
2. **Yüksek Yanıt Süresi**: Cache seviyelerini optimize edin
3. **Bellek Sorunları**: Eviction policy'yi ayarlayın
4. **Invalidation Sorunları**: Cascade depth'i kontrol edin

### Debug

```php
// Cache durumunu kontrol et
$health = app(CacheAnalyticsInterface::class)->getHealthStatus();

// Multi-level istatistikleri
$stats = app(MultiLevelCacheInterface::class)->getLevelStatistics();

// Invalidation geçmişi
$history = app(CacheInvalidatorInterface::class)->getInvalidationHistory();
```

## Sonuç

Advanced Caching System, ModularEcommerce projesinin cache performansını önemli ölçüde artırır ve gelişmiş cache yönetimi özellikleri sağlar. Sistem, akıllı warming, intelligent invalidation, multi-level caching ve detaylı analytics ile enterprise-level cache yönetimi sunar.
