import "./bootstrap";
import "../css/app.css";

import { createRoot } from "react-dom/client";
import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";

createInertiaApp({
    resolve: (name) =>
        resolvePageComponent(
            `${name}.jsx`,
            import.meta.glob("./Pages/**/*.jsx", { eager: true })
        ),
    setup({ el, App, props }) {
        createRoot(el).render(<App {...props} />);
    },
});
