import api from './api.js';

/**
 * CQRS API Service
 * Phase 4A: CQRS Integration Layer
 * 
 * Bu service CQRS pattern'e uygun olarak Command ve Query işlemlerini ayırır
 */
class CQRSApiService {
    constructor(apiService = api) {
        this.api = apiService;
        this.baseUrl = '/cqrs'; // CQRS endpoint'leri için base URL
    }

    // ==================== COMMAND OPERATIONS (Write) ====================

    /**
     * Ürün oluşturma command'ı
     */
    async createProduct(productData) {
        return await this.api.post(`${this.baseUrl}/products/commands/create`, {
            command: 'CreateProductCommand',
            data: productData
        });
    }

    /**
     * Ürün güncelleme command'ı
     */
    async updateProduct(productId, productData) {
        return await this.api.put(`${this.baseUrl}/products/commands/update/${productId}`, {
            command: 'UpdateProductCommand',
            data: productData
        });
    }

    /**
     * Ürün silme command'ı
     */
    async deleteProduct(productId) {
        return await this.api.delete(`${this.baseUrl}/products/commands/delete/${productId}`, {
            command: 'DeleteProductCommand'
        });
    }

    /**
     * Kategori oluşturma command'ı
     */
    async createCategory(categoryData) {
        return await this.api.post(`${this.baseUrl}/categories/commands/create`, {
            command: 'CreateCategoryCommand',
            data: categoryData
        });
    }

    /**
     * Kategori güncelleme command'ı
     */
    async updateCategory(categoryId, categoryData) {
        return await this.api.put(`${this.baseUrl}/categories/commands/update/${categoryId}`, {
            command: 'UpdateCategoryCommand',
            data: categoryData
        });
    }

    /**
     * Sipariş oluşturma command'ı
     */
    async createOrder(orderData) {
        return await this.api.post(`${this.baseUrl}/orders/commands/create`, {
            command: 'CreateOrderCommand',
            data: orderData
        });
    }

    /**
     * Sipariş durumu güncelleme command'ı
     */
    async updateOrderStatus(orderId, status) {
        return await this.api.put(`${this.baseUrl}/orders/commands/update-status/${orderId}`, {
            command: 'UpdateOrderStatusCommand',
            data: { status }
        });
    }

    // ==================== QUERY OPERATIONS (Read) ====================

    /**
     * Ürün listesi query'si
     */
    async getProducts(filters = {}) {
        const params = {
            query: 'GetProductsQuery',
            ...filters
        };
        
        return await this.api.get(`${this.baseUrl}/products/queries/list`, params);
    }

    /**
     * Tek ürün query'si
     */
    async getProduct(productId, options = {}) {
        const params = {
            query: 'GetProductQuery',
            include_variants: options.includeVariants || false,
            include_attributes: options.includeAttributes || false,
            include_images: options.includeImages || false
        };
        
        return await this.api.get(`${this.baseUrl}/products/queries/show/${productId}`, params);
    }

    /**
     * Ürün arama query'si
     */
    async searchProducts(searchTerm, filters = {}) {
        const params = {
            query: 'SearchProductsQuery',
            search: searchTerm,
            ...filters
        };
        
        return await this.api.get(`${this.baseUrl}/products/queries/search`, params);
    }

    /**
     * Kategori listesi query'si
     */
    async getCategories(filters = {}) {
        const params = {
            query: 'GetCategoriesQuery',
            ...filters
        };
        
        return await this.api.get(`${this.baseUrl}/categories/queries/list`, params);
    }

    /**
     * Kategori ağacı query'si
     */
    async getCategoryTree() {
        const params = {
            query: 'GetCategoryTreeQuery'
        };
        
        return await this.api.get(`${this.baseUrl}/categories/queries/tree`, params);
    }

    /**
     * Sipariş listesi query'si
     */
    async getOrders(filters = {}) {
        const params = {
            query: 'GetOrdersQuery',
            ...filters
        };
        
        return await this.api.get(`${this.baseUrl}/orders/queries/list`, params);
    }

    /**
     * Tek sipariş query'si
     */
    async getOrder(orderId) {
        const params = {
            query: 'GetOrderQuery'
        };
        
        return await this.api.get(`${this.baseUrl}/orders/queries/show/${orderId}`, params);
    }

    /**
     * Kullanıcı siparişleri query'si
     */
    async getUserOrders(userId, filters = {}) {
        const params = {
            query: 'GetUserOrdersQuery',
            user_id: userId,
            ...filters
        };
        
        return await this.api.get(`${this.baseUrl}/orders/queries/user-orders`, params);
    }

    // ==================== UTILITY METHODS ====================

    /**
     * CQRS bus durumunu kontrol et
     */
    async getCQRSStatus() {
        return await this.api.get(`${this.baseUrl}/status`);
    }

    /**
     * Command handler'ları listele
     */
    async getCommandHandlers() {
        return await this.api.get(`${this.baseUrl}/commands/handlers`);
    }

    /**
     * Query handler'ları listele
     */
    async getQueryHandlers() {
        return await this.api.get(`${this.baseUrl}/queries/handlers`);
    }

    /**
     * Cache'i temizle
     */
    async clearCache(cacheKey = null) {
        const data = cacheKey ? { cache_key: cacheKey } : {};
        return await this.api.post(`${this.baseUrl}/cache/clear`, data);
    }

    // ==================== BATCH OPERATIONS ====================

    /**
     * Toplu ürün işlemleri
     */
    async bulkProductOperation(operation, productIds, data = {}) {
        return await this.api.post(`${this.baseUrl}/products/commands/bulk`, {
            command: 'BulkProductCommand',
            operation,
            product_ids: productIds,
            data
        });
    }

    /**
     * Toplu kategori işlemleri
     */
    async bulkCategoryOperation(operation, categoryIds, data = {}) {
        return await this.api.post(`${this.baseUrl}/categories/commands/bulk`, {
            command: 'BulkCategoryCommand',
            operation,
            category_ids: categoryIds,
            data
        });
    }

    // ==================== PERFORMANCE MONITORING ====================

    /**
     * Query performance metrics
     */
    async getQueryMetrics(queryType = null) {
        const params = queryType ? { query_type: queryType } : {};
        return await this.api.get(`${this.baseUrl}/metrics/queries`, params);
    }

    /**
     * Command performance metrics
     */
    async getCommandMetrics(commandType = null) {
        const params = commandType ? { command_type: commandType } : {};
        return await this.api.get(`${this.baseUrl}/metrics/commands`, params);
    }
}

// Create default instance
const cqrsApi = new CQRSApiService();

export default cqrsApi;
export { CQRSApiService };
