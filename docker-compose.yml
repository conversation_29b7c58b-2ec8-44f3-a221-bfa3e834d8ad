version: '3.8'

services:
  # Laravel Uygulama ve Queue Worker'lar
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: modularecommerce_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - modularecommerce
    environment:
      - APP_ENV=${APP_ENV:-local}
      - APP_DEBUG=${APP_DEBUG:-true}
      - APP_KEY=${APP_KEY}
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=${DB_DATABASE:-modularecommerce}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
      - REDIS_PORT=6379
      - QUEUE_CONNECTION=redis
      - QUEUE_STRATEGY=${QUEUE_STRATEGY:-horizon}
      - AUTO_MIGRATE=${AUTO_MIGRATE:-false}
      - CLEAR_FAILED_JOBS=${CLEAR_FAILED_JOBS:-false}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "/usr/local/bin/queue-manager", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: modularecommerce_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    networks:
      - modularecommerce
    depends_on:
      - app

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: modularecommerce_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE:-modularecommerce}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-password}
      MYSQL_PASSWORD: ${DB_PASSWORD:-password}
      MYSQL_USER: ${DB_USERNAME:-laravel}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - modularecommerce
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: modularecommerce_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-null}
    volumes:
      - redis_data:/data
    networks:
      - modularecommerce
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Sadece Queue Worker'lar için ayrı container (opsiyonel)
  queue-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: modularecommerce_queue_worker
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - modularecommerce
    environment:
      - APP_ENV=${APP_ENV:-local}
      - APP_DEBUG=${APP_DEBUG:-true}
      - APP_KEY=${APP_KEY}
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=${DB_DATABASE:-modularecommerce}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
      - REDIS_PORT=6379
      - QUEUE_CONNECTION=redis
      - QUEUE_STRATEGY=workers  # Bu container sadece worker'ları çalıştırır
      - AUTO_MIGRATE=false
      - CLEAR_FAILED_JOBS=false
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    profiles:
      - workers-only  # Bu profil ile sadece worker'lar çalışır
    healthcheck:
      test: ["CMD", "/usr/local/bin/queue-manager", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Horizon Dashboard için ayrı container (opsiyonel)
  horizon:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: modularecommerce_horizon
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - modularecommerce
    environment:
      - APP_ENV=${APP_ENV:-local}
      - APP_DEBUG=${APP_DEBUG:-true}
      - APP_KEY=${APP_KEY}
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=${DB_DATABASE:-modularecommerce}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
      - REDIS_PORT=6379
      - QUEUE_CONNECTION=redis
      - QUEUE_STRATEGY=horizon  # Bu container sadece Horizon çalıştırır
      - AUTO_MIGRATE=false
      - CLEAR_FAILED_JOBS=false
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    profiles:
      - horizon-only  # Bu profil ile sadece Horizon çalışır
    healthcheck:
      test: ["CMD", "/usr/local/bin/queue-manager", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  modularecommerce:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
