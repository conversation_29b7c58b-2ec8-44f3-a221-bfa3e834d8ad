<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cache_events', function (Blueprint $table) {
            $table->id();
            $table->string('event', 50)->index(); // cache_hit, cache_miss, cache_error, etc.
            $table->string('cache_key', 500)->nullable()->index();
            $table->string('entity_type', 100)->nullable()->index();
            $table->string('entity_id', 100)->nullable()->index();
            $table->json('data')->nullable(); // Event-specific data
            $table->integer('response_time')->nullable(); // milliseconds
            $table->bigInteger('memory_usage')->nullable(); // bytes
            $table->integer('cache_level')->nullable(); // 1, 2, 3 for multi-level cache
            $table->string('cache_driver', 50)->nullable(); // redis, database, array
            $table->string('session_id', 100)->nullable()->index();
            $table->string('user_id', 100)->nullable()->index();
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['event', 'created_at']);
            $table->index(['entity_type', 'created_at']);
            $table->index(['cache_key', 'created_at']);
            $table->index(['created_at', 'event']);
            
            // Composite indexes for analytics
            $table->index(['event', 'entity_type', 'created_at']);
            $table->index(['cache_level', 'event', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cache_events');
    }
};
