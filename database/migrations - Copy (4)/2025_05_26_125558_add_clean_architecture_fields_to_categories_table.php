<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // Mevcut kolonları kontrol et ve sadece eksik olanları ekle
            if (!Schema::hasColumn('categories', 'image')) {
                $table->string('image')->nullable()->after('status');
            }
            if (!Schema::hasColumn('categories', 'icon')) {
                $table->string('icon')->nullable()->after('image');
            }
            if (!Schema::hasColumn('categories', 'featured')) {
                $table->boolean('featured')->default(false)->after('icon');
            }
            if (!Schema::hasColumn('categories', 'show_in_menu')) {
                $table->boolean('show_in_menu')->default(true)->after('featured');
            }
        });

        // Indexes'leri ayrı bir schema call'da ekle
        Schema::table('categories', function (Blueprint $table) {
            // Indexes ekle (eğer yoksa)
            try {
                $table->index(['status', 'position'], 'categories_status_position_index');
                $table->index(['parent_id', 'position'], 'categories_parent_position_index');
                $table->index(['featured', 'status'], 'categories_featured_status_index');
                $table->index(['show_in_menu', 'status'], 'categories_menu_status_index');
            } catch (\Exception $e) {
                // Index zaten varsa devam et
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // Indexes'leri kaldır
            $table->dropIndex(['status', 'position']);
            $table->dropIndex(['parent_id', 'position']);
            $table->dropIndex(['featured', 'status']);
            $table->dropIndex(['show_in_menu', 'status']);

            // Alanları kaldır
            $table->dropColumn(['position', 'image', 'icon', 'featured', 'show_in_menu']);
        });
    }
};
